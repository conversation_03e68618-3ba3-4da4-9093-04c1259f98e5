tinymce.Resource.add('tinymce.html-i18n.help-keynav.sv_SE',
'<h1>Påbörja tangentbordsnavigering</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Fokusera på menyraden</dt>\n' +
  '  <dd>Windows eller Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Fokusera på verktygsraden</dt>\n' +
  '  <dd>Windows eller Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Fokusera på verktygsraden</dt>\n' +
  '  <dd>Windows eller Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Fokusera aviseringen</dt>\n' +
  '  <dd>Windows eller Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Fokusera på en snabbverktygsrad</dt>\n' +
  '  <dd>Windows, Linux eller macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Navigeringen börjar vid det första gränssnittsobjektet, vilket är markerat eller understruket om det gäller det första objektet i\n' +
  '  sidfotens elementsökväg.</p>\n' +
  '\n' +
  '<h1>Navigera mellan UI-avsnitt</h1>\n' +
  '\n' +
  '<p>Flytta från ett UI-avsnitt till nästa genom att trycka på <strong>Tabb</strong>.</p>\n' +
  '\n' +
  '<p>Flytta från ett UI-avsnitt till det föregående genom att trycka på <strong>Skift+Tabb</strong>.</p>\n' +
  '\n' +
  '<p><strong>Tabb</strong>-ordningen för dessa UI-avsnitt är:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Menyrad</li>\n' +
  '  <li>Varje verktygsradsgrupp</li>\n' +
  '  <li>Sidoruta</li>\n' +
  '  <li>Elementsökväg i sidfoten</li>\n' +
  '  <li>Växlingsknapp för ordantal i sidfoten</li>\n' +
  '  <li>Varumärkeslänk i sidfoten</li>\n' +
  '  <li>Storlekshandtag för redigeraren i sidfoten</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Om ett UI-avsnitt inte finns hoppas det över.</p>\n' +
  '\n' +
  '<p>Om sidfoten har fokus på tangentbordsnavigering, och det inte finns någon synlig sidoruta, flyttas fokus till den första verktygsradsgruppen\n' +
  '  när du trycker på <strong>Skift+Tabb</strong>, inte till den sista.</p>\n' +
  '\n' +
  '<h1>Navigera i UI-avsnitt</h1>\n' +
  '\n' +
  '<p>Flytta från ett UI-element till nästa genom att trycka på motsvarande <strong>piltangent</strong>.</p>\n' +
  '\n' +
  '<p><strong>Vänsterpil</strong> och <strong>högerpil</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>flytta mellan menyer på menyraden.</li>\n' +
  '  <li>öppna en undermeny på en meny.</li>\n' +
  '  <li>flytta mellan knappar i en verktygsradgrupp.</li>\n' +
  '  <li>flytta mellan objekt i sidfotens elementsökväg.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Nedpil</strong> och <strong>uppil</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>flytta mellan menyalternativ på en meny.</li>\n' +
  '  <li>flytta mellan alternativ på en popup-meny på verktygsraden.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Piltangenterna</strong> cirkulerar inom det fokuserade UI-avsnittet.</p>\n' +
  '\n' +
  '<p>Tryck på <strong>Esc</strong>-tangenten om du vill stänga en öppen meny, undermeny eller popup-meny.</p>\n' +
  '\n' +
  '<p>Om det aktuella fokuset är högst upp i ett UI-avsnitt avlutas även tangentbordsnavigeringen helt när\n' +
  '  du trycker på <strong>Esc</strong>-tangenten.</p>\n' +
  '\n' +
  '<h1>Köra ett menyalternativ eller en verktygfältsknapp</h1>\n' +
  '\n' +
  '<p>När menyalternativet eller verktygsradsknappen är markerad trycker du på <strong>Retur</strong>, <strong>Enter</strong>\n' +
  '  eller <strong>blanksteg</strong> för att köra alternativet.</p>\n' +
  '\n' +
  '<h1>Navigera i dialogrutor utan flikar</h1>\n' +
  '\n' +
  '<p>I dialogrutor utan flikar är den första interaktiva komponenten i fokus när dialogrutan öppnas.</p>\n' +
  '\n' +
  '<p>Navigera mellan interaktiva dialogkomponenter genom att trycka på <strong>Tabb</strong> eller <strong>Skift+Tabb</strong>.</p>\n' +
  '\n' +
  '<h1>Navigera i dialogrutor med flikar</h1>\n' +
  '\n' +
  '<p>I dialogrutor utan flikar är den första knappen på flikmenyn i fokus när dialogrutan öppnas.</p>\n' +
  '\n' +
  '<p>Navigera mellan interaktiva komponenter på dialogrutefliken genom att trycka på <strong>Tabb</strong> eller\n' +
  '  <strong>Skift+Tabb</strong>.</p>\n' +
  '\n' +
  '<p>Växla till en annan dialogruta genom att fokusera på flikmenyn och sedan trycka på motsvarande <strong>piltangent</strong>\n' +
  '  för att cirkulera mellan de tillgängliga flikarna.</p>\n');