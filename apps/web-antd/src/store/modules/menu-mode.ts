import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useAccessStore, useTabbarStore } from '@vben/stores';
import { customerListMenu, workspaceMenus, localRoutes } from '#/router/routes/local';
import { cloneDeep } from 'lodash-es';
import type { MenuRecordRaw, RouteMeta } from '@vben/types';
import type { RouteRecordStringComponent } from '@vben/types';
import { useRouter } from 'vue-router';

/**
 * 菜单模式类型
 */
export type MenuMode = 'customer-list' | 'workspace';

/**
 * 菜单模式管理store
 */
export const useMenuModeStore = defineStore(
  'menu-mode',
  () => {
    // 当前菜单模式，默认为客户列表模式
    const currentMode = ref<MenuMode>('customer-list');
    const router = useRouter();
    const accessStore = useAccessStore();





    // 获取当前菜单模式
    const getCurrentMode = () => {
      return currentMode.value;
    };

    // 是否为客户列表模式
    const isCustomerListMode = () => {
      return currentMode.value === 'customer-list';
    };

    // 是否为工作台模式
    const isWorkspaceMode = () => {
      return currentMode.value === 'workspace';
    };

    // 检查路由是否属于工作台菜单
    const isWorkspaceRoute = (route: RouteRecordStringComponent): boolean => {
      const routePath = route.path.replace(/^\//, '').split('/')[0]; // 获取路径的第一段
      return workspaceMenus.some(w => w.path.replace(/^\//, '').split('/')[0] === routePath);
    };

    // 检查路由是否属于客户列表菜单
    const isCustomerRoute = (route: RouteRecordStringComponent): boolean => {
      const routePath = route.path.replace(/^\//, '').split('/')[0]; // 获取路径的第一段
      return customerListMenu.some(c => c.path.replace(/^\//, '').split('/')[0] === routePath);
    };

    // 递归处理菜单项
    const processMenuItem = (route: RouteRecordStringComponent): RouteRecordStringComponent => {
      const isWorkspaceMenu = isWorkspaceRoute(route);
      const isCustomerMenu = isCustomerRoute(route);
      
      // 在客户列表模式下隐藏工作台菜单，在工作台模式下隐藏客户列表菜单
      const shouldHide = (isCustomerListMode() && isWorkspaceMenu) || 
                       (isWorkspaceMode() && isCustomerMenu);

      // 确保meta和title存在
      const meta = (route.meta || {}) as RouteMeta;
      
      const processedRoute = {
        ...route,
        meta: { 
          ...meta,
          hideInMenu: shouldHide || meta.hideInMenu,
          hideInBreadcrumb: shouldHide || meta.hideInBreadcrumb,
        },
      } as RouteRecordStringComponent;

      // 递归处理子菜单
      if (processedRoute.children && processedRoute.children.length > 0) {
        processedRoute.children = processedRoute.children.map(child => processMenuItem(child));
      }

      return processedRoute;
    };

    // 获取当前模式下的菜单
    const getCurrentMenus = (): RouteRecordStringComponent[] => {
      // 根据当前模式过滤菜单
      let filteredMenus;
      if (isCustomerListMode()) {
        filteredMenus = [...customerListMenu];
      } else {
        filteredMenus = [...workspaceMenus];
      }
      
      // 添加本地路由
      const allMenus = cloneDeep([...filteredMenus, ...localRoutes]);
      return allMenus.map(route => processMenuItem(route));
    };

    // 设置菜单模式
    const setMenuMode = async (mode: MenuMode) => {
      if (mode === currentMode.value) return;

      const oldMode = currentMode.value;
      currentMode.value = mode;

      try {
        // 强制清除所有已打开的标签页（在菜单切换时）
        const tabbarStore = useTabbarStore();
        // 直接清空所有标签页，不保留任何标签页
        tabbarStore.tabs = [];
        tabbarStore.updateCacheTabs();

        // 更新菜单 - 使用 regenerateMenus 来确保正确的菜单生成流程
        const { regenerateMenus } = await import('#/router/access');
        await regenerateMenus();
      } catch (error) {
        // 如果出错，恢复原来的模式
        currentMode.value = oldMode;
        console.error('Failed to switch menu mode:', error);
        throw error;
      }
    };

    return {
      getCurrentMode,
      isCustomerListMode,
      isWorkspaceMode,
      setMenuMode,
    };
  },
  {
    persist: true,
  }
);
