import { ref } from 'vue';

import { defineStore } from 'pinia';

export interface VoucherDetail {
  summary: string;
  account: string;
  debit: number;
  credit: number;
  account_code: string;
  parent_account_info: null | string;
  auxiliary_accounting_list: Array<{
    id: number;
    name: string;
    type: string;
  }>;
  confidence: number;
}

export interface VoucherData {
  request_id: string;
  company_info: {
    business_scope: string;
    company_name: string;
    company_type: string;
    customer_id: string;
    tax_id: string;
  };
  voucher_type: string;
  vouchers: Array<{
    details: VoucherDetail[];
    executor: string;
    record_date: string;
    source: string[];
    type: string;
  }>;
}

// 银行回单生成凭证的数据结构
export interface GeneratedVoucherDetail {
  account_code: string;
  account_name: string;
  summary: string;
  debit: number;
  credit: number;
}

export interface GeneratedVoucher {
  voucher_number: string;
  voucher_type: string;
  date: string;
  description: string;
  currency: string;
  details: GeneratedVoucherDetail[];
  source_data: {
    account_number: string;
    conversion_method: string;
    original_amount: number;
    transaction_id: string;
  };
}

export interface VoucherGenerationData {
  conversion_summary: {
    balance_check: boolean;
    failed_conversions: number;
    success_rate: number;
    successful_conversions: number;
    total_credit_amount: number;
    total_debit_amount: number;
    total_records_processed: number;
    total_vouchers_generated: number;
  };
  generated_vouchers: GeneratedVoucher[];
  validation_results: {
    balance_issues: any[];
    invalid_vouchers: number;
    total_vouchers: number;
    valid_vouchers: number;
    validation_errors: any[];
  };
  conversion_errors: any[];
  metadata: {
    conversion_time: number;
    total_processing_time: string;
    voucher_settings: {
      auto_balance: boolean;
      default_currency: string;
      rounding_precision: number;
      voucher_prefix: string;
    };
  };
}

// 编辑凭证数据接口
export interface ReviewVoucherData {
  id: string;
  code: string;
  date: string;
  type: string;
  confirmed: boolean;
  source_type: string;
  executor: string;
  totalAmount: number;
  debit: number;
  credit: number;
  detail: Array<{
    id: number;
    summary: string;
    subjectName: string;
    debit: number;
    credit: number;
    quantity?: number | null;
    unit_price?: number | null;
    auxiliary?: any[];
  }>;
  // 原始完整数据，用于传递给编辑页面
  originalData?: any;
}

export const useVoucherStore = defineStore('voucher', () => {
  const currentVoucher = ref<null | VoucherData>(null);
  // 存储银行回单生成的凭证数据
  const generatedVoucherData = ref<null | VoucherGenerationData>(null);
  // 存储原始银行回单数据
  const bankReceiptData = ref<any | null>(null);
  // 存储待编辑的凭证数据
  const reviewVoucherData = ref<null | ReviewVoucherData>(null);
  // 存储凭证查看页面的滚动位置
  const voucherViewScrollPosition = ref<number>(0);
  // 存储完整的凭证列表，用于导航
  const voucherList = ref<any[]>([]);
  // 存储当前凭证在列表中的索引
  const currentVoucherIndex = ref<number>(-1);

  // Mock data for testing
  const mockVoucherData: VoucherData = {
    request_id: 'REQ20250602001',
    company_info: {
      customer_id: '54892',
      company_name: '青岛绿洲印象文化传媒有限公司',
      tax_id: '********',
      company_type: '小规模纳税人',
      business_scope:
        '技术开发、技术咨询、技术服务；销售计算机、软件及辅助设备。',
    },
    voucher_type: '银行回单',
    vouchers: [
      {
        type: '记',
        record_date: '2025-06-03',
        details: [
          {
            summary: '广告制作费,广告制作费',
            account: '销售费用-广告费',
            debit: 156_000,
            credit: 0,
            account_code: 'null',
            parent_account_info: '9222721081396962762,5601,销售费用',
            auxiliary_accounting_list: [{ id: 1, name: 'xxx', type: 'xx' }],
            confidence: 0.9,
          },
          {
            summary: '广告制作费,广告制作费',
            account: '银行存款',
            debit: 0,
            credit: 156_000,
            account_code: '9222721081396962589',
            parent_account_info: null,
            auxiliary_accounting_list: [],
            confidence: 0.9,
          },
          {
            summary: '品牌策划费,品牌策划费',
            account: '管理费用-咨询费',
            debit: 89_500,
            credit: 0,
            account_code: '9222721081396962785',
            parent_account_info: null,
            auxiliary_accounting_list: [],
            confidence: 0.9,
          },
          {
            summary: '品牌策划费,品牌策划费',
            account: '银行存款',
            debit: 0,
            credit: 89_500,
            account_code: '9222721081396962589',
            parent_account_info: null,
            auxiliary_accounting_list: [],
            confidence: 0.9,
          },
          {
            summary: '未知场景:外包服务费支出,外包设计费',
            account: '未知场景',
            debit: 35_000,
            credit: 0,
            account_code: 'null',
            parent_account_info: null,
            auxiliary_accounting_list: [],
            confidence: 0.9,
          },
          {
            summary: '未知场景2:外包服务费支出,外包设计费',
            account: '未知场景2',
            debit: 35_000,
            credit: 0,
            account_code: 'null',
            parent_account_info: null,
            auxiliary_accounting_list: [],
            confidence: 0.9,
          },
        ],
        source: ['BR001', 'BR002', 'BR003'],
        executor: 'llm',
      },
    ],
  };

  function $reset() {
    currentVoucher.value = null;
    generatedVoucherData.value = null;
    bankReceiptData.value = null;
  }

  function setVoucherData(data: VoucherData) {
    currentVoucher.value = data;
  }

  function clearVoucherData() {
    currentVoucher.value = null;
  }

  // 设置银行回单生成的凭证数据
  function setGeneratedVoucherData(data: VoucherGenerationData) {
    generatedVoucherData.value = data;
  }

  // 清除银行回单生成的凭证数据
  function clearGeneratedVoucherData() {
    generatedVoucherData.value = null;
  }

  // 设置银行回单数据
  function setBankReceiptData(data: any) {
    bankReceiptData.value = data;
  }

  // 清除银行回单数据
  function clearBankReceiptData() {
    bankReceiptData.value = null;
  }

  // 设置待编辑的凭证数据
  function setReviewVoucherData(data: ReviewVoucherData) {
    reviewVoucherData.value = data;
  }

  // 清除待编辑的凭证数据
  function clearReviewVoucherData() {
    reviewVoucherData.value = null;
  }

  // 设置凭证查看页面滚动位置
  function setVoucherViewScrollPosition(position: number) {
    voucherViewScrollPosition.value = position;
  }

  // 获取凭证查看页面滚动位置
  function getVoucherViewScrollPosition() {
    return voucherViewScrollPosition.value;
  }

  // 清除凭证查看页面滚动位置
  function clearVoucherViewScrollPosition() {
    voucherViewScrollPosition.value = 0;
  }

  // 设置凭证列表和当前索引
  function setVoucherListWithIndex(list: any[], currentId: string) {
    voucherList.value = list;
    currentVoucherIndex.value = list.findIndex(item => item.id === currentId);
  }

  // 获取上一张凭证
  function getPreviousVoucher() {
    if (currentVoucherIndex.value > 0) {
      return voucherList.value[currentVoucherIndex.value - 1];
    }
    return null;
  }

  // 获取下一张凭证
  function getNextVoucher() {
    if (currentVoucherIndex.value < voucherList.value.length - 1) {
      return voucherList.value[currentVoucherIndex.value + 1];
    }
    return null;
  }

  // 检查是否有上一张凭证
  function hasPreviousVoucher() {
    return currentVoucherIndex.value > 0;
  }

  // 检查是否有下一张凭证
  function hasNextVoucher() {
    return currentVoucherIndex.value < voucherList.value.length - 1;
  }

  // 获取当前凭证位置信息
  function getCurrentVoucherPosition() {
    return {
      current: currentVoucherIndex.value + 1,
      total: voucherList.value.length,
      hasPrevious: hasPreviousVoucher(),
      hasNext: hasNextVoucher(),
    };
  }

  // 清除凭证列表和索引
  function clearVoucherListAndIndex() {
    voucherList.value = [];
    currentVoucherIndex.value = -1;
  }

  // 初始化 mock 数据
  setVoucherData(mockVoucherData);

  return {
    currentVoucher,
    generatedVoucherData,
    bankReceiptData,
    reviewVoucherData,
    voucherViewScrollPosition,
    voucherList,
    currentVoucherIndex,
    setVoucherData,
    clearVoucherData,
    setGeneratedVoucherData,
    clearGeneratedVoucherData,
    setBankReceiptData,
    clearBankReceiptData,
    setReviewVoucherData,
    clearReviewVoucherData,
    setVoucherViewScrollPosition,
    getVoucherViewScrollPosition,
    clearVoucherViewScrollPosition,
    setVoucherListWithIndex,
    getPreviousVoucher,
    getNextVoucher,
    hasPreviousVoucher,
    hasNextVoucher,
    getCurrentVoucherPosition,
    clearVoucherListAndIndex,
    $reset,
  };
});
