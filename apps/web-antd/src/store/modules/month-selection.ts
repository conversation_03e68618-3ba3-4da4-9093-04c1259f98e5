import type { Dayjs } from 'dayjs';

import { ref } from 'vue';

import dayjs from 'dayjs';
import { defineStore } from 'pinia';

export const useMonthSelectionStore = defineStore(
  'month-selection',
  () => {
    // 存储选中的月份，默认为当前月份
    const selectedMonth = ref<Dayjs>(dayjs());

    // 确保 selectedMonth 是有效的 dayjs 对象
    const ensureValidDayjs = (): Dayjs => {
      // 检查 selectedMonth.value 是否是有效的 dayjs 对象
      if (!selectedMonth.value || typeof selectedMonth.value.format !== 'function') {
        // 如果不是有效的 dayjs 对象，尝试转换
        if (typeof selectedMonth.value === 'string') {
          // 如果是字符串，尝试解析
          const parsed = dayjs(selectedMonth.value);
          if (parsed.isValid()) {
            selectedMonth.value = parsed;
          } else {
            // 如果解析失败，使用当前月份
            selectedMonth.value = dayjs();
          }
        } else {
          // 如果是其他类型，使用当前月份
          selectedMonth.value = dayjs();
        }
      }
      return selectedMonth.value;
    };

    // 设置选中的月份
    const setSelectedMonth = (month: Dayjs) => {
      selectedMonth.value = month;
    };

    // 获取格式化的月份字符串 (YYYYMM格式)
    const getFormattedMonth = () => {
      const validMonth = ensureValidDayjs();
      return validMonth.format('YYYYMM');
    };

    // 获取年月分离的格式
    const getYearMonth = () => {
      const validMonth = ensureValidDayjs();
      return {
        year: validMonth.year(),
        month: validMonth.month() + 1, // dayjs的月份是0-11，需要+1
      };
    };

    return {
      selectedMonth,
      setSelectedMonth,
      getFormattedMonth,
      getYearMonth,
      ensureValidDayjs,
    };
  },
  {
    persist: true, // 持久化存储
  },
);
