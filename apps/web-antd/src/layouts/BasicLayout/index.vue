import { useMenuModeStore } from '#/store/modules/menu-mode'; import { message }
from 'ant-design-vue'; import { ArrowLeftOutlined } from
'@ant-design/icons-vue'; const menuModeStore = useMenuModeStore(); //
返回客户列表 const handleBackToCustomerList = async () => { try { //
显示加载状态 message.loading({ content: '正在返回客户列表...', key: 'switchMode'
}); // 切换模式 menuModeStore.switchToCustomerList(); // 等待菜单更新完成 await
new Promise((resolve) => setTimeout(resolve, 300)); //
重新加载页面以确保菜单正确显示 window.location.href = '/company-status'; //
显示成功提示 message.success({ content: '已返回客户列表', key: 'switchMode',
duration: 2 }); } catch (error) { console.error('返回客户列表失败:', error);
message.error({ content: '返回客户列表失败，请重试', key: 'switchMode',
duration: 2 }); } };

<script lang="ts" setup>
  import { ArrowLeftOutlined } from '@ant-design/icons-vue';
  import { Button as AButton, message } from 'ant-design-vue';

  import { useMenuModeStore } from '#/store/modules/menu-mode';

  const menuModeStore = useMenuModeStore();

  // 返回客户列表
  const handleBackToCustomerList = async () => {
    try {
      // 显示加载状态
      message.loading({ content: '正在返回客户列表...', key: 'switchMode' });

      // 切换模式
      menuModeStore.switchToCustomerList();

      // 等待菜单更新完成
      await new Promise((resolve) => setTimeout(resolve, 300));

      // 重新加载页面以确保菜单正确显示
      window.location.href = '/company-status';

      // 显示成功提示
      message.success({
        content: '已返回客户列表',
        duration: 2,
        key: 'switchMode',
      });
    } catch (error) {
      console.error('返回客户列表失败:', error);
      message.error({
        content: '返回客户列表失败，请重试',
        duration: 2,
        key: 'switchMode',
      });
    }
  };
</script>

<template>
  <div class="h-full">
    <!-- 顶部导航栏 -->
    <div class="flex items-center border-b border-gray-200 bg-white px-4 py-2">
      <!-- 返回按钮 -->
      <AButton
        v-if="!menuModeStore.isCustomerListMode()"
        type="link"
        @click="handleBackToCustomerList"
        class="mr-4"
      >
        <ArrowLeftOutlined />
        返回客户列表
      </AButton>

      <!-- 其他导航栏内容 -->
      <slot name="header"></slot>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 p-4">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
  .h-full {
    height: 100%;
  }
</style>
