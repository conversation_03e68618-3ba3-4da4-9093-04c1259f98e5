import { useAccessStore } from '@vben/stores';

// API基础路径
const API_BASE_PATH = '/prod-api/autojob/api';
// const API_BASE_PATH = '/api';

// 简化的响应类型
interface ApiResponse<T = any> {
  status: string;
  message: string;
  data: T;
}

// 请求缓存接口
interface RequestCache {
  promise: Promise<any>;
  timestamp: number;
}

// 简单的HTTP请求工具（带防抖功能）
class SimpleHttpClient {
  private baseURL: string;
  private requestCache: Map<string, RequestCache> = new Map();
  private readonly CACHE_DURATION = 3000; // 3秒内相同请求使用缓存

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  // 生成请求的唯一标识
  private generateRequestKey(method: string, url: string, options: any): string {
    const { params, data } = options;
    return JSON.stringify({ method, url, params, data });
  }

  // 清理过期的缓存
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, cache] of this.requestCache.entries()) {
      if (now - cache.timestamp > this.CACHE_DURATION) {
        this.requestCache.delete(key);
      }
    }
  }

  private async request<T>(
    method: string,
    url: string,
    options: {
      params?: Record<string, any>;
      data?: any;
      skipCache?: boolean; // 是否跳过缓存
    } = {}
  ): Promise<T> {
    const { params, data, skipCache = false } = options;

    // 生成请求唯一标识
    const requestKey = this.generateRequestKey(method, url, { params, data });

    // 清理过期缓存
    this.cleanExpiredCache();

    // 检查是否有相同的请求正在进行（防抖处理）
    if (!skipCache && this.requestCache.has(requestKey)) {
      const cachedRequest = this.requestCache.get(requestKey)!;
      const now = Date.now();
      const timeDiff = now - cachedRequest.timestamp;

      // 如果缓存未过期，返回缓存的 Promise
      if (timeDiff < this.CACHE_DURATION) {
        return cachedRequest.promise;
      } else {
        // 缓存已过期，删除
        this.requestCache.delete(requestKey);
      }
    }

    // 创建实际的请求 Promise
    const requestPromise = this.executeRequest<T>(method, url, params, data);

    // 将请求缓存起来（如果不跳过缓存）
    if (!skipCache) {
      this.requestCache.set(requestKey, {
        promise: requestPromise,
        timestamp: Date.now(),
      });
    }

    try {
      const result = await requestPromise;
      return result;
    } catch (error) {
      // 请求失败时，从缓存中移除
      if (!skipCache) {
        this.requestCache.delete(requestKey);
      }
      throw error;
    }
  }

  // 执行实际的HTTP请求
  private async executeRequest<T>(
    method: string,
    url: string,
    params?: Record<string, any>,
    data?: any
  ): Promise<T> {
    // 构建完整URL
    let fullUrl = `${this.baseURL}${url}`;

    // 添加查询参数
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        fullUrl += `?${queryString}`;
      }
    }

    // 请求配置
    const config: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    // 添加Authorization头
    const accessStore = useAccessStore();
    if (accessStore.accessToken) {
      (config.headers as Record<string, string>).Authorization = `Bearer ${accessStore.accessToken}`;
    }

    // 添加请求体
    if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(fullUrl, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('请求失败:', error);
      throw error;
    }
  }

  async get<T>(url: string, options?: { params?: Record<string, any>; skipCache?: boolean }): Promise<T> {
    return this.request<T>('GET', url, options);
  }

  async post<T>(url: string, data?: any, options?: { skipCache?: boolean }): Promise<T> {
    return this.request<T>('POST', url, { data, ...options });
  }

  async put<T>(url: string, data?: any, options?: { skipCache?: boolean }): Promise<T> {
    return this.request<T>('PUT', url, { data, ...options });
  }

  async delete<T>(url: string, options?: { data?: any; skipCache?: boolean }): Promise<T> {
    return this.request<T>('DELETE', url, options);
  }

  // 文件上传方法
  async upload<T>(url: string, formData: FormData, options?: { skipCache?: boolean }): Promise<T> {
    const { skipCache = true } = options || {}; // 文件上传默认跳过缓存

    // 构建完整URL
    const fullUrl = `${this.baseURL}${url}`;

    // 请求配置
    const config: RequestInit = {
      method: 'POST',
      body: formData,
      // 不设置 Content-Type，让浏览器自动设置 multipart/form-data 边界
    };

    // 添加Authorization头
    const accessStore = useAccessStore();
    if (accessStore.accessToken) {
      config.headers = {
        'Authorization': `Bearer ${accessStore.accessToken}`,
      };
    }

    try {
      const response = await fetch(fullUrl, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('文件上传失败:', error);
      throw error;
    }
  }

  // 清理所有缓存
  clearCache(): void {
    this.requestCache.clear();
  }

  // 获取缓存状态（用于调试）
  getCacheInfo(): { size: number; keys: string[] } {
    return {
      size: this.requestCache.size,
      keys: Array.from(this.requestCache.keys()),
    };
  }
}

// 创建HTTP客户端实例
const httpClient = new SimpleHttpClient(API_BASE_PATH);

// 创建文件上传客户端实例（使用不同的基础路径）
const uploadClient = new SimpleHttpClient('/prod-api/autojob');

// 统一的API调用处理
async function apiCall<T>(request: () => Promise<ApiResponse<T>>): Promise<T> {
  try {
    const result = await request();

    if (result.status === 'success') {
      return result.data;
    }
    throw new Error(result.message || '请求失败');
  } catch (error: any) {
    console.error('API请求失败:', error);
    throw error;
  }
}

// 导出类型和实例
export type { ApiResponse };
export { httpClient, apiCall, uploadClient };
