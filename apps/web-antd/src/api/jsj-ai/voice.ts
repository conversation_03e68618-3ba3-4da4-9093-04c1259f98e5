import { httpClient, uploadClient } from './request';
import type { UploadResponse } from './upload';

export interface VoiceProcessRequest {
  oss_url: string;
}

export interface VoiceRecognitionResult {
  success: boolean;
  text: string;
  confidence: number;
}

export interface VoiceCommandResult {
  success: boolean;
  action: string;
  description: string;
  matched_text: string;
  confidence: number;
  original_text: string;
  executable: boolean;
}

export interface VoiceProcessResponse {
  status: 'success' | 'error';
  message: string;
  data: {
    recognition: VoiceRecognitionResult;
    command: VoiceCommandResult;
  };
}

/**
 * 上传语音文件（PCM格式）
 * @param audioBlob - 语音数据blob（PCM格式）
 * @param options - 上传选项
 * @returns Promise<UploadResponse> 返回上传响应数据
 */
export async function uploadVoiceFile(
  audioBlob: Blob,
  options?: { company_name?: string },
): Promise<UploadResponse> {
  const formData = new FormData();
  
  // 创建一个临时文件名，以.pcm结尾
  const fileName = `voice_${Date.now()}.pcm`;
  const file = new File([audioBlob], fileName, { type: 'audio/pcm' });
  
  formData.append('file', file);

  // 如果提供了公司名称，添加到表单数据中
  if (options?.company_name) {
    formData.append('company_name', options.company_name);
  }

  return uploadClient.upload<UploadResponse>('/upload', formData);
}

/**
 * 处理语音识别和命令匹配
 * @param request - 语音处理请求参数
 * @returns Promise<VoiceProcessResponse> 返回语音处理结果
 */
export async function processVoice(
  request: VoiceProcessRequest,
): Promise<VoiceProcessResponse> {
  return httpClient.post<VoiceProcessResponse>('/voice/process', request);
}

/**
 * 完整的语音处理流程：上传文件 -> 语音识别和命令匹配
 * @param audioBlob - 语音数据blob（PCM格式）
 * @param options - 处理选项
 * @returns Promise<VoiceProcessResponse> 返回语音处理结果
 */
export async function processVoiceComplete(
  audioBlob: Blob,
  options?: { company_name?: string },
): Promise<VoiceProcessResponse> {
  // 1. 上传语音文件
  const uploadResult = await uploadVoiceFile(audioBlob, options);
  
  if (uploadResult.status !== 'success' || !uploadResult.files || uploadResult.files.length === 0) {
    throw new Error('语音文件上传失败');
  }

  // 2. 处理语音识别和命令匹配
  const ossUrl = uploadResult.files[0]?.file_path;
  if (!ossUrl) {
    throw new Error('文件上传成功但未返回文件路径');
  }
  
  const processResult = await processVoice({ oss_url: ossUrl });

  return processResult;
}