# 凭证管理系统 Mock 服务器

这是一个用于测试凭证总览功能的 WebSocket Mock 服务器。

## 功能特性

- 提供模拟的凭证数据
- 支持 WebSocket 实时通信
- 支持凭证的增删改查操作
- 模拟实时推送新凭证
- 包含多种类型的凭证数据（进项发票、销项发票、银行回单、工资单）

## 安装依赖

```bash
cd apps/web-antd/src/mock
npm install
```

## 启动服务器

### 开发模式（自动重启）
```bash
npm run dev
```

### 生产模式
```bash
npm start
```

服务器将在 `ws://localhost:8080` 启动。

## WebSocket API

### 客户端发送的消息类型

1. **获取所有凭证**
```json
{
  "type": "get_vouchers"
}
```

2. **更新凭证**
```json
{
  "type": "update_voucher",
  "voucher": {
    "id": 1,
    "type": "记",
    "record_date": "2025-01-15",
    "details": [...],
    "executor": "people",
    "reviewed": true,
    "source_type": "进项发票",
    "source_info": {...}
  }
}
```

3. **删除凭证**
```json
{
  "type": "delete_voucher",
  "id": 1
}
```

4. **获取原始数据详情**
```json
{
  "type": "get_source_detail",
  "voucher_id": 1
}
```

### 服务器发送的消息类型

1. **连接成功**
```json
{
  "type": "connected",
  "message": "已连接到凭证管理系统"
}
```

2. **凭证列表**
```json
{
  "type": "vouchers_list",
  "vouchers": [...]
}
```

3. **凭证已更新**
```json
{
  "type": "voucher_updated",
  "voucher": {...}
}
```

4. **凭证已删除**
```json
{
  "type": "voucher_deleted",
  "id": 1
}
```

5. **新增凭证**
```json
{
  "type": "voucher_added",
  "voucher": {...}
}
```

6. **原始数据详情**
```json
{
  "type": "source_detail",
  "voucher_id": 1,
  "detail": {...}
}
```

7. **错误消息**
```json
{
  "type": "error",
  "message": "错误描述"
}
```

## 模拟数据说明

服务器包含 6 条预设的凭证数据，涵盖了所有支持的凭证类型：

1. **进项发票凭证** - 购买办公用品
2. **销项发票凭证** - 销售商品
3. **银行回单凭证** - 银行转账手续费
4. **工资单凭证** - 计提工资
5. **进项发票凭证** - 采购原材料
6. **银行回单凭证** - 月末结转成本

每 30 秒会有 10% 的概率自动生成新的测试凭证，用于演示实时推送功能。

## 注意事项

- 确保端口 8080 未被其他程序占用
- 前端应用需要连接到 `ws://localhost:8080/ws`
- 所有数据都存储在内存中，重启服务器会重置数据
- 这只是一个测试用的 Mock 服务器，不适用于生产环境
