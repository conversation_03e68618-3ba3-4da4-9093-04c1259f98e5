#!/bin/bash

# 凭证管理系统 Mock 服务器启动脚本

echo "==================================="
echo "  凭证管理系统 Mock 服务器"
echo "==================================="

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

# 检查是否安装了 npm
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到 npm，请先安装 npm"
    exit 1
fi

# 进入 mock 目录
cd "$(dirname "$0")"

echo "📁 当前目录: $(pwd)"

# 检查是否存在 package.json
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到 package.json 文件"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
fi

# 检查端口是否被占用
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 8080 已被占用"
    echo "请关闭占用端口 8080 的程序，或修改服务器配置使用其他端口"
    exit 1
fi

echo "🚀 启动 WebSocket 服务器..."
echo "📡 服务器地址: ws://localhost:8080"
echo "📖 API 文档: 请查看 README.md"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "==================================="

# 启动服务器
npm start
