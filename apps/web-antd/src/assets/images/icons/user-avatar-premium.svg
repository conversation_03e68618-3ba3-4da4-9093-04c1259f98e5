<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <!-- 主背景渐变 -->
    <radialGradient id="userPremiumGradient" cx="50%" cy="40%" r="60%">
      <stop offset="0%" stop-color="#52c41a" />
      <stop offset="50%" stop-color="#73d13d" />
      <stop offset="100%" stop-color="#389e0d" />
    </radialGradient>
    
    <!-- 用户头像渐变 -->
    <linearGradient id="userFaceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" />
      <stop offset="30%" stop-color="#fefefe" />
      <stop offset="70%" stop-color="#f6ffed" />
      <stop offset="100%" stop-color="#d9f7be" />
    </linearGradient>
    
    <!-- 头发渐变 -->
    <linearGradient id="userHairPremium" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8c8c8c" />
      <stop offset="50%" stop-color="#595959" />
      <stop offset="100%" stop-color="#434343" />
    </linearGradient>
    
    <!-- 衣服渐变 -->
    <linearGradient id="userClothesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e6f7ff" />
      <stop offset="50%" stop-color="#bae7ff" />
      <stop offset="100%" stop-color="#91d5ff" />
    </linearGradient>
    
    <!-- 发光滤镜 -->
    <filter id="userPremiumGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 阴影滤镜 -->
    <filter id="userPremiumShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="2" flood-color="rgba(0,0,0,0.2)"/>
    </filter>
  </defs>
  
  <!-- 主背景圆形 -->
  <circle cx="100" cy="100" r="88" fill="url(#userPremiumGradient)" />
  
  <!-- 外层光环 -->
  <circle cx="100" cy="100" r="82" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1">
    <animate attributeName="r" values="82;86;82" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite" />
  </circle>
  
  <!-- 用户头像主体 -->
  <g filter="url(#userPremiumShadow)">
    <!-- 头部轮廓 -->
    <circle cx="100" cy="68" r="32" fill="url(#userFaceGradient)" />
    
    <!-- 头发 -->
    <path d="M68 50 Q100 35 132 50 Q130 40 100 38 Q70 40 68 50 Z" 
          fill="url(#userHairPremium)" />
    <!-- 头发细节 -->
    <path d="M75 45 Q85 40 95 45 M105 45 Q115 40 125 45" 
          stroke="url(#userHairPremium)" stroke-width="1.5" fill="none" opacity="0.6" />
    
    <!-- 身体/肩膀 -->
    <path d="M100 95 C80 95, 60 105, 55 135 C55 145, 60 155, 70 160 L130 160 C140 155, 145 145, 145 135 C140 105, 120 95, 100 95 Z" 
          fill="url(#userClothesGradient)" />
    
    <!-- 衣领 -->
    <path d="M85 95 Q100 105 115 95 Q100 110 85 95" 
          fill="url(#userFaceGradient)" opacity="0.8" />
  </g>
  
  <!-- 面部特征 -->
  <g>
    <!-- 眉毛 -->
    <path d="M88 62 Q92 60 96 62" stroke="#8c8c8c" stroke-width="1.5" stroke-linecap="round" fill="none" opacity="0.7" />
    <path d="M104 62 Q108 60 112 62" stroke="#8c8c8c" stroke-width="1.5" stroke-linecap="round" fill="none" opacity="0.7" />
    
    <!-- 眼睛 -->
    <ellipse cx="90" cy="68" rx="4" ry="3" fill="#262626" />
    <ellipse cx="110" cy="68" rx="4" ry="3" fill="#262626" />
    <!-- 眼睛高光 -->
    <circle cx="91" cy="67" r="1.2" fill="#ffffff" opacity="0.9" />
    <circle cx="111" cy="67" r="1.2" fill="#ffffff" opacity="0.9" />
    <circle cx="92" cy="68" r="0.5" fill="#ffffff" opacity="0.6" />
    <circle cx="112" cy="68" r="0.5" fill="#ffffff" opacity="0.6" />
    
    <!-- 鼻子 -->
    <ellipse cx="100" cy="75" rx="1.5" ry="3" fill="#d9d9d9" opacity="0.4" />
    <path d="M98 77 Q100 78 102 77" stroke="#bfbfbf" stroke-width="0.8" fill="none" opacity="0.5" />
    
    <!-- 嘴巴 -->
    <path d="M92 85 Q100 92 108 85" stroke="#52c41a" stroke-width="2.5" stroke-linecap="round" fill="none" opacity="0.8" />
    <!-- 嘴唇细节 -->
    <path d="M95 87 Q100 89 105 87" stroke="#73d13d" stroke-width="1" stroke-linecap="round" fill="none" opacity="0.6" />
    
    <!-- 脸颊红晕 -->
    <ellipse cx="78" cy="78" rx="6" ry="4" fill="#ff7875" opacity="0.15" />
    <ellipse cx="122" cy="78" rx="6" ry="4" fill="#ff7875" opacity="0.15" />
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.5">
    <!-- 左侧装饰 -->
    <g>
      <circle cx="25" cy="65" r="2" fill="#ffffff" />
      <circle cx="25" cy="100" r="3" fill="#73d13d" opacity="0.8">
        <animate attributeName="opacity" values="0.4;0.9;0.4" dur="3s" repeatCount="indefinite" />
      </circle>
      <circle cx="25" cy="135" r="2" fill="#ffffff" />
      <path d="M25 70 L40 70 M25 105 L35 105 M25 140 L40 140" 
            stroke="#ffffff" stroke-width="1" stroke-linecap="round" />
    </g>
    
    <!-- 右侧装饰 -->
    <g>
      <circle cx="175" cy="65" r="2" fill="#ffffff" />
      <circle cx="175" cy="100" r="3" fill="#73d13d" opacity="0.8">
        <animate attributeName="opacity" values="0.9;0.4;0.9" dur="3s" repeatCount="indefinite" />
      </circle>
      <circle cx="175" cy="135" r="2" fill="#ffffff" />
      <path d="M175 70 L160 70 M175 105 L165 105 M175 140 L160 140" 
            stroke="#ffffff" stroke-width="1" stroke-linecap="round" />
    </g>
    
    <!-- 顶部装饰 -->
    <circle cx="60" cy="25" r="2" fill="#ffffff" />
    <circle cx="100" cy="18" r="4" fill="#73d13d" opacity="0.8">
      <animate attributeName="r" values="3;5;3" dur="4s" repeatCount="indefinite" />
      <animate attributeName="opacity" values="0.5;0.9;0.5" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="140" cy="25" r="2" fill="#ffffff" />
    
    <!-- 底部装饰 -->
    <circle cx="60" cy="175" r="2" fill="#ffffff" />
    <circle cx="140" cy="175" r="2" fill="#ffffff" />
  </g>
  
  <!-- 外层脉冲环 -->
  <circle cx="100" cy="100" r="92" stroke="rgba(255,255,255,0.3)" stroke-width="1" fill="none">
    <animate attributeName="r" values="88;95;88" dur="5s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.4;0.1" dur="5s" repeatCount="indefinite" />
  </circle>
  
  <!-- 内层脉冲环 -->
  <circle cx="100" cy="100" r="78" stroke="rgba(115,209,61,0.4)" stroke-width="0.8" fill="none">
    <animate attributeName="r" values="75;82;75" dur="3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="3s" repeatCount="indefinite" />
  </circle>
</svg>
