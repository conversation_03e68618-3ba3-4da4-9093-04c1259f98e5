<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <!-- 主背景渐变 -->
    <radialGradient id="cuteUserBg" cx="50%" cy="40%" r="60%">
      <stop offset="0%" stop-color="#fff5f5" />
      <stop offset="50%" stop-color="#ffe8e8" />
      <stop offset="100%" stop-color="#ffd6d6" />
    </radialGradient>
    
    <!-- 人物皮肤渐变 -->
    <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffeaa7" />
      <stop offset="50%" stop-color="#fdcb6e" />
      <stop offset="100%" stop-color="#e17055" />
    </linearGradient>
    
    <!-- 头发渐变 -->
    <linearGradient id="hairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#6c5ce7" />
      <stop offset="50%" stop-color="#a29bfe" />
      <stop offset="100%" stop-color="#5f3dc4" />
    </linearGradient>
    
    <!-- 衣服渐变 -->
    <linearGradient id="clothesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#74b9ff" />
      <stop offset="50%" stop-color="#0984e3" />
      <stop offset="100%" stop-color="#2d3436" />
    </linearGradient>
    
    <!-- 光环渐变 -->
    <radialGradient id="userHaloGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#fd79a8" />
      <stop offset="70%" stop-color="#e84393" />
      <stop offset="100%" stop-color="#d63031" />
    </radialGradient>
    
    <!-- 发光滤镜 -->
    <filter id="userCuteGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 阴影滤镜 -->
    <filter id="userCuteShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="rgba(0,0,0,0.15)"/>
    </filter>
  </defs>
  
  <!-- 主背景圆形 -->
  <circle cx="100" cy="100" r="88" fill="url(#cuteUserBg)" />
  
  <!-- 光环装饰 -->
  <ellipse cx="100" cy="45" rx="20" ry="6" fill="url(#userHaloGradient)" opacity="0.8">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite" />
    <animate attributeName="rx" values="18;22;18" dur="3s" repeatCount="indefinite" />
  </ellipse>
  
  <!-- 星星装饰 -->
  <g opacity="0.9">
    <g transform="translate(140, 40)">
      <path d="M0,-8 L2,-2 L8,-2 L3,1 L5,7 L0,4 L-5,7 L-3,1 L-8,-2 L-2,-2 Z" 
            fill="#fd79a8">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" />
        <animateTransform attributeName="transform" type="rotate" 
                         values="0;360" dur="8s" repeatCount="indefinite" />
      </path>
    </g>
  </g>
  
  <!-- 人物头部 -->
  <circle cx="100" cy="80" r="35" 
          fill="url(#skinGradient)" filter="url(#userCuteShadow)" />
  
  <!-- 头发 -->
  <path d="M65 60 Q100 40 135 60 Q130 50 100 45 Q70 50 65 60 Z" 
        fill="url(#hairGradient)" filter="url(#userCuteShadow)" />
  
  <!-- 头发细节 -->
  <path d="M75 55 Q85 50 95 55 M105 55 Q115 50 125 55" 
        stroke="url(#hairGradient)" stroke-width="2" fill="none" opacity="0.7" />
  
  <!-- 眼睛 -->
  <g>
    <!-- 左眼 -->
    <ellipse cx="88" cy="75" rx="4" ry="6" fill="#2d3436" />
    <ellipse cx="88" cy="73" rx="2" ry="3" fill="#ffffff" />
    <circle cx="89" cy="74" r="1" fill="#2d3436" />
    
    <!-- 右眼 -->
    <ellipse cx="112" cy="75" rx="4" ry="6" fill="#2d3436" />
    <ellipse cx="112" cy="73" rx="2" ry="3" fill="#ffffff" />
    <circle cx="111" cy="74" r="1" fill="#2d3436" />
  </g>
  
  <!-- 鼻子 -->
  <ellipse cx="100" cy="82" rx="1.5" ry="2" fill="#e17055" opacity="0.6" />
  
  <!-- 嘴巴（微笑） -->
  <path d="M92 88 Q100 95 108 88" 
        stroke="#e17055" stroke-width="2" fill="none" stroke-linecap="round" />
  
  <!-- 腮红 -->
  <ellipse cx="75" cy="85" rx="6" ry="4" fill="#fd79a8" opacity="0.3" />
  <ellipse cx="125" cy="85" rx="6" ry="4" fill="#fd79a8" opacity="0.3" />
  
  <!-- 身体 -->
  <ellipse cx="100" cy="140" rx="30" ry="25" 
           fill="url(#clothesGradient)" filter="url(#userCuteShadow)" />
  
  <!-- 衣领 -->
  <path d="M85 115 Q100 125 115 115 Q100 135 85 115" 
        fill="url(#skinGradient)" opacity="0.9" />
  
  <!-- 装饰光点 -->
  <g opacity="0.7">
    <!-- 左侧 -->
    <circle cx="30" cy="70" r="2" fill="#fd79a8">
      <animate attributeName="opacity" values="0.4;1;0.4" dur="2s" repeatCount="indefinite" />
    </circle>
    <circle cx="25" cy="100" r="1.5" fill="#74b9ff">
      <animate attributeName="opacity" values="1;0.4;1" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="35" cy="130" r="2" fill="#fd79a8">
      <animate attributeName="opacity" values="0.4;1;0.4" dur="2.5s" repeatCount="indefinite" />
    </circle>
    
    <!-- 右侧 -->
    <circle cx="170" cy="70" r="2" fill="#fd79a8">
      <animate attributeName="opacity" values="1;0.4;1" dur="2s" repeatCount="indefinite" />
    </circle>
    <circle cx="175" cy="100" r="1.5" fill="#74b9ff">
      <animate attributeName="opacity" values="0.4;1;0.4" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="165" cy="130" r="2" fill="#fd79a8">
      <animate attributeName="opacity" values="1;0.4;1" dur="2.5s" repeatCount="indefinite" />
    </circle>
  </g>
  
  <!-- 外层脉冲环 -->
  <circle cx="100" cy="100" r="92" stroke="rgba(253,121,168,0.3)" stroke-width="1" fill="none">
    <animate attributeName="r" values="88;95;88" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite" />
  </circle>
  
  <!-- 内层脉冲环 -->
  <circle cx="100" cy="100" r="78" stroke="rgba(116,185,255,0.4)" stroke-width="0.8" fill="none">
    <animate attributeName="r" values="75;82;75" dur="3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.3;0.7;0.3" dur="3s" repeatCount="indefinite" />
  </circle>
</svg>
