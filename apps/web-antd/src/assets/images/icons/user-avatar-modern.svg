<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- 渐变和滤镜定义 -->
  <defs>
    <!-- 主背景渐变 -->
    <radialGradient id="userMainGradient" cx="50%" cy="40%" r="60%">
      <stop offset="0%" stop-color="#52c41a" />
      <stop offset="50%" stop-color="#73d13d" />
      <stop offset="100%" stop-color="#389e0d" />
    </radialGradient>

    <!-- 用户头像渐变 -->
    <linearGradient id="userAvatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" />
      <stop offset="50%" stop-color="#f6ffed" />
      <stop offset="100%" stop-color="#d9f7be" />
    </linearGradient>

    <!-- 头发渐变 -->
    <linearGradient id="userHairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8c8c8c" />
      <stop offset="50%" stop-color="#595959" />
      <stop offset="100%" stop-color="#434343" />
    </linearGradient>

    <!-- 发光滤镜 -->
    <filter id="userGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 阴影滤镜 -->
    <filter id="userShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="2" flood-color="rgba(0,0,0,0.2)"/>
    </filter>
  </defs>

  <!-- 主背景圆形 -->
  <circle cx="100" cy="100" r="88" fill="url(#userMainGradient)" />

  <!-- 外层光环 -->
  <circle cx="100" cy="100" r="82" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1">
    <animate attributeName="r" values="82;86;82" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite" />
  </circle>

  <!-- 用户头像主体 -->
  <g filter="url(#userShadow)">
    <!-- 头部轮廓 -->
    <circle cx="100" cy="70" r="30" fill="url(#userAvatarGradient)" />

    <!-- 头发 -->
    <path d="M70 55 Q100 40 130 55 Q125 45 100 45 Q75 45 70 55 Z"
          fill="url(#userHairGradient)" />

    <!-- 身体/肩膀 -->
    <path d="M100 95 C85 95, 65 105, 60 130 C60 140, 65 150, 75 155 L125 155 C135 150, 140 140, 140 130 C135 105, 115 95, 100 95 Z"
          fill="url(#userAvatarGradient)" />
  </g>

  <!-- 面部特征 -->
  <g>
    <!-- 眼睛 -->
    <ellipse cx="90" cy="68" rx="3" ry="2" fill="#262626" />
    <ellipse cx="110" cy="68" rx="3" ry="2" fill="#262626" />
    <circle cx="91" cy="67" r="0.8" fill="#ffffff" opacity="0.8" />
    <circle cx="111" cy="67" r="0.8" fill="#ffffff" opacity="0.8" />

    <!-- 鼻子 -->
    <ellipse cx="100" cy="75" rx="1" ry="2" fill="#d9d9d9" opacity="0.6" />

    <!-- 微笑 -->
    <path d="M92 82 Q100 90 108 82" stroke="#52c41a" stroke-width="2.5" stroke-linecap="round" fill="none" opacity="0.8" />
  </g>

  <!-- 装饰元素 -->
  <g opacity="0.5">
    <!-- 左侧装饰 -->
    <g>
      <circle cx="25" cy="70" r="2" fill="#ffffff" />
      <circle cx="25" cy="100" r="3" fill="#73d13d" opacity="0.8" />
      <circle cx="25" cy="130" r="2" fill="#ffffff" />
      <path d="M25 75 L40 75 M25 105 L35 105 M25 135 L40 135"
            stroke="#ffffff" stroke-width="1" stroke-linecap="round" />
    </g>

    <!-- 右侧装饰 -->
    <g>
      <circle cx="175" cy="70" r="2" fill="#ffffff" />
      <circle cx="175" cy="100" r="3" fill="#73d13d" opacity="0.8" />
      <circle cx="175" cy="130" r="2" fill="#ffffff" />
      <path d="M175 75 L160 75 M175 105 L165 105 M175 135 L160 135"
            stroke="#ffffff" stroke-width="1" stroke-linecap="round" />
    </g>

    <!-- 顶部装饰 -->
    <circle cx="60" cy="25" r="2" fill="#ffffff" />
    <circle cx="100" cy="20" r="3" fill="#73d13d" opacity="0.8" />
    <circle cx="140" cy="25" r="2" fill="#ffffff" />

    <!-- 底部装饰 -->
    <circle cx="60" cy="175" r="2" fill="#ffffff" />
    <circle cx="140" cy="175" r="2" fill="#ffffff" />
  </g>

  <!-- 外层脉冲环 -->
  <circle cx="100" cy="100" r="92" stroke="rgba(255,255,255,0.3)" stroke-width="1" fill="none">
    <animate attributeName="r" values="88;95;88" dur="5s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.4;0.1" dur="5s" repeatCount="indefinite" />
  </circle>

  <!-- 内层脉冲环 -->
  <circle cx="100" cy="100" r="78" stroke="rgba(115,209,61,0.4)" stroke-width="0.8" fill="none">
    <animate attributeName="r" values="75;82;75" dur="3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="3s" repeatCount="indefinite" />
  </circle>
</svg>
