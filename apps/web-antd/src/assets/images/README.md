# 图片资源管理指南

## 目录结构

```
src/assets/images/
├── logos/          # Logo 相关图片
├── ai/            # AI 相关图标  
├── backgrounds/   # 背景图片
├── icons/         # 功能图标
└── index.ts       # 统一导出文件
```

## 使用方式

### 1. 模块导入（推荐）
```typescript
import { LogoJsj, DoubaoAiIconModern } from '#/assets/images';

// 在组件中使用
<img :src="LogoJsj" alt="Logo" />
```

### 2. 路径常量
```typescript
import { IMAGE_PATHS } from '#/assets/images';

// 使用路径常量
<img :src="IMAGE_PATHS.logos.jsj" alt="Logo" />
```

## 添加新图片

1. 将图片放入对应分类目录
2. 在 `index.ts` 中添加导出：
   ```typescript
   export { default as NewImage } from './category/new-image.png';
   ```
3. 更新 `IMAGE_PATHS` 常量（如需要）

## 与 public 目录的区别

- **src/assets**: 会被构建工具处理、优化、哈希化
- **public**: 直接复制，不处理，适合 favicon、第三方库等

## 最佳实践

- 组件中使用的图片 → `src/assets/images`
- SEO 相关文件 → `public`
- 第三方库文件 → `public`
- 需要固定路径的文件 → `public`
