<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- 渐变和滤镜定义 -->
  <defs>
    <!-- 主背景渐变 -->
    <radialGradient id="aiMainGradient" cx="50%" cy="40%" r="60%">
      <stop offset="0%" stop-color="#667eea" />
      <stop offset="50%" stop-color="#764ba2" />
      <stop offset="100%" stop-color="#4c63d2" />
    </radialGradient>

    <!-- 机器人头部渐变 -->
    <linearGradient id="aiHeadGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" />
      <stop offset="50%" stop-color="#f8faff" />
      <stop offset="100%" stop-color="#e8f2ff" />
    </linearGradient>

    <!-- 眼部发光效果 -->
    <radialGradient id="aiEyeGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#00d4ff" />
      <stop offset="70%" stop-color="#0099cc" />
      <stop offset="100%" stop-color="#006699" />
    </radialGradient>

    <!-- 发光滤镜 -->
    <filter id="aiGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 内阴影效果 -->
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
      <feOffset dx="2" dy="2" result="offset"/>
    </filter>
  </defs>

  <!-- 主背景圆形 -->
  <circle cx="100" cy="100" r="88" fill="url(#aiMainGradient)" />

  <!-- 外层光环 -->
  <circle cx="100" cy="100" r="82" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1">
    <animate attributeName="r" values="82;86;82" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite" />
  </circle>

  <!-- 机器人头部主体 -->
  <rect x="60" y="55" width="80" height="70" rx="25" ry="25"
        fill="url(#aiHeadGradient)" filter="url(#aiGlow)" />

  <!-- 机器人天线 -->
  <g opacity="0.8">
    <line x1="85" y1="55" x2="85" y2="45" stroke="#ffffff" stroke-width="3" stroke-linecap="round" />
    <line x1="115" y1="55" x2="115" y2="45" stroke="#ffffff" stroke-width="3" stroke-linecap="round" />
    <circle cx="85" cy="42" r="4" fill="#00d4ff" />
    <circle cx="115" cy="42" r="4" fill="#00d4ff" />
  </g>

  <!-- 机器人眼部 -->
  <g>
    <!-- 左眼 -->
    <ellipse cx="85" cy="80" rx="8" ry="6" fill="url(#aiEyeGlow)" />
    <circle cx="85" cy="80" r="3" fill="#ffffff" opacity="0.9" />
    <circle cx="86" cy="79" r="1" fill="#00d4ff" />

    <!-- 右眼 -->
    <ellipse cx="115" cy="80" rx="8" ry="6" fill="url(#aiEyeGlow)" />
    <circle cx="115" cy="80" r="3" fill="#ffffff" opacity="0.9" />
    <circle cx="116" cy="79" r="1" fill="#00d4ff" />
  </g>

  <!-- 机器人嘴部 -->
  <rect x="90" y="95" width="20" height="8" rx="4" ry="4"
        fill="#667eea" opacity="0.6" />

  <!-- 机器人身体 -->
  <rect x="70" y="125" width="60" height="45" rx="15" ry="15"
        fill="url(#aiHeadGradient)" opacity="0.9" filter="url(#aiGlow)" />

  <!-- 胸部指示灯 -->
  <circle cx="100" cy="145" r="6" fill="#00d4ff" opacity="0.8">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="2s" repeatCount="indefinite" />
  </circle>
  <circle cx="100" cy="145" r="3" fill="#ffffff" opacity="0.6" />
  
  <!-- 豆包笑脸 - 更平滑的曲线 -->
  <path d="M75 115 Q100 140 125 115" stroke="#0052D9" stroke-width="6" stroke-linecap="round" fill="none" />
  
  <!-- 顶部光晕效果 -->
  <circle cx="75" cy="65" r="10" fill="white" opacity="0.6" />
  
  <!-- AI元素 - 更精细的电路图案 -->
  <g opacity="0.7">
    <!-- 左侧电路 -->
    <path d="M25 90 L45 90 L45 100" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="25" cy="90" r="2" fill="#FFFFFF" />
    
    <!-- 右侧电路 -->
    <path d="M175 90 L155 90 L155 100" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="175" cy="90" r="2" fill="#FFFFFF" />
    
    <!-- 顶部电路 -->
    <path d="M90 25 L90 45 L100 45" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="90" cy="25" r="2" fill="#FFFFFF" />
    
    <!-- 底部电路 -->
    <path d="M110 175 L110 155 L100 155" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="110" cy="175" r="2" fill="#FFFFFF" />
  </g>
  
  <!-- 脉冲动画效果 - 更细致 -->
  <circle cx="100" cy="100" r="95" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.2">
    <animate attributeName="r" values="90;100;90" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.3;0.1" dur="4s" repeatCount="indefinite" />
  </circle>
  
  <!-- 额外的脉冲环 -->
  <circle cx="100" cy="100" r="80" stroke="#FFFFFF" stroke-width="0.5" fill="none" opacity="0.15">
    <animate attributeName="r" values="80;85;80" dur="3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.2;0.1" dur="3s" repeatCount="indefinite" />
  </circle>
</svg>
