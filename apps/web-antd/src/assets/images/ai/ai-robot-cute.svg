<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <!-- 背景渐变 -->
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#e3f2fd" />
      <stop offset="100%" stop-color="#bbdefb" />
    </radialGradient>

    <!-- 机器人头部渐变 -->
    <linearGradient id="headGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" />
      <stop offset="100%" stop-color="#f5f5f5" />
    </linearGradient>

    <!-- 眼睛发光渐变 -->
    <radialGradient id="eyeGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#42a5f5" />
      <stop offset="100%" stop-color="#1976d2" />
    </radialGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.1)"/>
    </filter>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" />

  <!-- 机器人头部 -->
  <rect x="60" y="60" width="80" height="80" rx="25" ry="25"
        fill="url(#headGradient)" filter="url(#shadow)" />

  <!-- 天线 -->
  <g>
    <line x1="85" y1="60" x2="85" y2="45" stroke="#666" stroke-width="3" stroke-linecap="round" />
    <line x1="115" y1="60" x2="115" y2="45" stroke="#666" stroke-width="3" stroke-linecap="round" />
    <circle cx="85" cy="42" r="4" fill="#42a5f5">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite" />
    </circle>
    <circle cx="115" cy="42" r="4" fill="#42a5f5">
      <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite" />
    </circle>
  </g>

  <!-- 眼睛 -->
  <g>
    <!-- 左眼 -->
    <circle cx="85" cy="85" r="12" fill="url(#eyeGradient)" />
    <circle cx="85" cy="85" r="8" fill="#ffffff" />
    <circle cx="87" cy="83" r="4" fill="#1976d2" />
    <circle cx="88" cy="82" r="1.5" fill="#ffffff" />

    <!-- 右眼 -->
    <circle cx="115" cy="85" r="12" fill="url(#eyeGradient)" />
    <circle cx="115" cy="85" r="8" fill="#ffffff" />
    <circle cx="117" cy="83" r="4" fill="#1976d2" />
    <circle cx="118" cy="82" r="1.5" fill="#ffffff" />
  </g>

  <!-- 嘴巴 -->
  <path d="M85 110 Q100 120 115 110" stroke="#666" stroke-width="3"
        fill="none" stroke-linecap="round" />

  <!-- 脸颊装饰 -->
  <circle cx="65" cy="95" r="6" fill="#ffcdd2" opacity="0.6" />
  <circle cx="135" cy="95" r="6" fill="#ffcdd2" opacity="0.6" />

  <!-- 身体 -->
  <rect x="70" y="140" width="60" height="40" rx="15" ry="15"
        fill="url(#headGradient)" filter="url(#shadow)" />

  <!-- 胸前指示灯 -->
  <circle cx="100" cy="155" r="6" fill="#4caf50" opacity="0.8">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="1.5s" repeatCount="indefinite" />
  </circle>

  <!-- 装饰线条 -->
  <g stroke="#e0e0e0" stroke-width="1">
    <line x1="75" y1="150" x2="125" y2="150" />
    <line x1="75" y1="165" x2="125" y2="165" />
  </g>

  <!-- 手臂 -->
  <ellipse cx="50" cy="155" rx="8" ry="20" fill="url(#headGradient)" filter="url(#shadow)" />
  <ellipse cx="150" cy="155" rx="8" ry="20" fill="url(#headGradient)" filter="url(#shadow)" />

  <!-- 装饰星星 -->
  <g opacity="0.6">
    <g transform="translate(30,50)">
      <path d="M0,-8 L2,-2 L8,-2 L3,2 L5,8 L0,4 L-5,8 L-3,2 L-8,-2 L-2,-2 Z" fill="#ffc107">
        <animateTransform attributeName="transform" type="rotate"
                         values="0;360" dur="4s" repeatCount="indefinite" />
      </path>
    </g>
    <g transform="translate(170,60)">
      <path d="M0,-6 L1.5,-1.5 L6,-1.5 L2.5,1.5 L4,6 L0,3 L-4,6 L-2.5,1.5 L-6,-1.5 L-1.5,-1.5 Z" fill="#ff9800">
        <animateTransform attributeName="transform" type="rotate"
                         values="360;0" dur="3s" repeatCount="indefinite" />
      </path>
    </g>
  </g>
</svg>
