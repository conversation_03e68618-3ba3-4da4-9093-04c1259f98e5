<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <!-- 主背景渐变 -->
    <radialGradient id="robotMainGradient" cx="50%" cy="40%" r="60%">
      <stop offset="0%" stop-color="#667eea" />
      <stop offset="50%" stop-color="#764ba2" />
      <stop offset="100%" stop-color="#4c63d2" />
    </radialGradient>
    
    <!-- 机器人头部渐变 -->
    <linearGradient id="robotHeadGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" />
      <stop offset="50%" stop-color="#f8faff" />
      <stop offset="100%" stop-color="#e8f2ff" />
    </linearGradient>
    
    <!-- 眼部发光效果 -->
    <radialGradient id="robotEyeGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#00d4ff" />
      <stop offset="70%" stop-color="#0099cc" />
      <stop offset="100%" stop-color="#006699" />
    </radialGradient>
    
    <!-- 发光滤镜 -->
    <filter id="robotGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 阴影滤镜 -->
    <filter id="robotShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- 主背景圆形 -->
  <circle cx="100" cy="100" r="88" fill="url(#robotMainGradient)" />
  
  <!-- 外层光环 -->
  <circle cx="100" cy="100" r="82" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1">
    <animate attributeName="r" values="82;86;82" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite" />
  </circle>
  
  <!-- 机器人头部主体 -->
  <rect x="55" y="50" width="90" height="75" rx="30" ry="30" 
        fill="url(#robotHeadGradient)" filter="url(#robotShadow)" />
  
  <!-- 机器人天线 -->
  <g opacity="0.9">
    <line x1="80" y1="50" x2="80" y2="35" stroke="#ffffff" stroke-width="3" stroke-linecap="round" />
    <line x1="120" y1="50" x2="120" y2="35" stroke="#ffffff" stroke-width="3" stroke-linecap="round" />
    <circle cx="80" cy="32" r="5" fill="#00d4ff">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite" />
    </circle>
    <circle cx="120" cy="32" r="5" fill="#00d4ff">
      <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite" />
    </circle>
  </g>
  
  <!-- 机器人眼部 -->
  <g>
    <!-- 左眼外框 -->
    <ellipse cx="80" cy="75" rx="12" ry="8" fill="#4c63d2" opacity="0.3" />
    <ellipse cx="80" cy="75" rx="10" ry="6" fill="url(#robotEyeGlow)" />
    <circle cx="80" cy="75" r="4" fill="#ffffff" opacity="0.9" />
    <circle cx="81" cy="74" r="1.5" fill="#00d4ff" />
    
    <!-- 右眼外框 -->
    <ellipse cx="120" cy="75" rx="12" ry="8" fill="#4c63d2" opacity="0.3" />
    <ellipse cx="120" cy="75" rx="10" ry="6" fill="url(#robotEyeGlow)" />
    <circle cx="120" cy="75" r="4" fill="#ffffff" opacity="0.9" />
    <circle cx="121" cy="74" r="1.5" fill="#00d4ff" />
  </g>
  
  <!-- 机器人嘴部 -->
  <rect x="85" y="95" width="30" height="10" rx="5" ry="5" 
        fill="#667eea" opacity="0.7" />
  <rect x="90" y="97" width="20" height="6" rx="3" ry="3" 
        fill="#4c63d2" opacity="0.5" />
  
  <!-- 机器人身体 -->
  <rect x="65" y="125" width="70" height="50" rx="20" ry="20" 
        fill="url(#robotHeadGradient)" opacity="0.95" filter="url(#robotShadow)" />
  
  <!-- 胸部指示灯 -->
  <circle cx="100" cy="145" r="8" fill="#00d4ff" opacity="0.3" />
  <circle cx="100" cy="145" r="6" fill="#00d4ff" opacity="0.6">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" />
  </circle>
  <circle cx="100" cy="145" r="3" fill="#ffffff" opacity="0.8" />
  
  <!-- 身体装饰线条 -->
  <g opacity="0.4">
    <line x1="75" y1="135" x2="125" y2="135" stroke="#4c63d2" stroke-width="1" />
    <line x1="75" y1="155" x2="125" y2="155" stroke="#4c63d2" stroke-width="1" />
    <line x1="75" y1="165" x2="125" y2="165" stroke="#4c63d2" stroke-width="1" />
  </g>
  
  <!-- 科技装饰元素 -->
  <g opacity="0.5">
    <!-- 左侧装饰 -->
    <g>
      <circle cx="25" cy="80" r="2" fill="#ffffff" />
      <circle cx="25" cy="100" r="3" fill="#00d4ff" opacity="0.8" />
      <circle cx="25" cy="120" r="2" fill="#ffffff" />
      <path d="M25 85 L40 85 M25 105 L35 105 M25 125 L40 125" 
            stroke="#ffffff" stroke-width="1" stroke-linecap="round" />
    </g>
    
    <!-- 右侧装饰 -->
    <g>
      <circle cx="175" cy="80" r="2" fill="#ffffff" />
      <circle cx="175" cy="100" r="3" fill="#00d4ff" opacity="0.8" />
      <circle cx="175" cy="120" r="2" fill="#ffffff" />
      <path d="M175 85 L160 85 M175 105 L165 105 M175 125 L160 125" 
            stroke="#ffffff" stroke-width="1" stroke-linecap="round" />
    </g>
    
    <!-- 顶部装饰 -->
    <circle cx="60" cy="25" r="2" fill="#ffffff" />
    <circle cx="100" cy="20" r="3" fill="#00d4ff" opacity="0.8" />
    <circle cx="140" cy="25" r="2" fill="#ffffff" />
    
    <!-- 底部装饰 -->
    <circle cx="60" cy="175" r="2" fill="#ffffff" />
    <circle cx="140" cy="175" r="2" fill="#ffffff" />
  </g>
  
  <!-- 外层脉冲环 -->
  <circle cx="100" cy="100" r="92" stroke="rgba(255,255,255,0.3)" stroke-width="1" fill="none">
    <animate attributeName="r" values="88;95;88" dur="5s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.4;0.1" dur="5s" repeatCount="indefinite" />
  </circle>
  
  <!-- 内层脉冲环 -->
  <circle cx="100" cy="100" r="78" stroke="rgba(0,212,255,0.4)" stroke-width="0.8" fill="none">
    <animate attributeName="r" values="75;82;75" dur="3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="3s" repeatCount="indefinite" />
  </circle>
</svg>
