# Assets vs Public 目录对比

## 功能对比

| 特性 | src/assets | public |
|------|------------|--------|
| 构建处理 | ✅ 会被处理 | ❌ 不处理 |
| 文件哈希 | ✅ 自动添加 | ❌ 保持原名 |
| 压缩优化 | ✅ 自动压缩 | ❌ 原样复制 |
| 模块导入 | ✅ 支持 import | ❌ 只能路径访问 |
| TypeScript | ✅ 类型检查 | ❌ 无类型 |
| 热更新 | ✅ 支持 HMR | ❌ 需刷新页面 |
| 缓存控制 | ✅ 哈希控制 | ❌ 手动控制 |

## 适用场景

### src/assets 适合：
- 组件中使用的图片
- 需要优化的资源
- 经常变动的文件
- 需要 import 的资源

### public 适合：
- favicon.ico
- robots.txt
- 第三方库文件
- SEO 相关文件
- 固定路径的资源
