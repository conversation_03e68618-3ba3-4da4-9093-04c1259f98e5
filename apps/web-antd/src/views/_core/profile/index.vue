<script setup lang="ts">
// import type { UserProfile } from '#/api/system/profile/model'; // 已删除system模块
// import { userProfile } from '#/api/system/profile'; // 已删除system模块

import { onMounted, onUnmounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

// 临时类型定义，用户资料功能已移除
interface UserProfile {
  avatar?: string;
  email?: string;
  nickname?: string;
  phone?: string;
  sex?: number;
}

// 临时空函数，用户资料功能已移除
const userProfile = async (): Promise<UserProfile> => ({});
import { useAuthStore } from '#/store';

import { emitter } from './mitt';
import ProfilePanel from './profile-panel.vue';
import SettingPanel from './setting-panel.vue';

const profile = ref<UserProfile>();
async function loadProfile() {
  const resp = await userProfile();
  profile.value = resp;
}

onMounted(loadProfile);

const authStore = useAuthStore();
const userStore = useUserStore();
/**
 * ToDo 接口重复
 */
async function handleUploadFinish() {
  // 重新加载用户信息
  await loadProfile();
  // 更新store
  const userInfo = await authStore.fetchUserInfo();
  userStore.setUserInfo(userInfo);
}

onMounted(() => emitter.on('updateProfile', loadProfile));
onUnmounted(() => emitter.off('updateProfile'));
</script>

<template>
  <Page>
    <div class="flex flex-col gap-[16px] lg:flex-row">
      <!-- 左侧 -->
      <ProfilePanel :profile="profile" @upload-finish="handleUploadFinish" />
      <!-- 右侧 -->
      <SettingPanel
        v-if="profile"
        :profile="profile"
        class="flex-1 overflow-hidden"
      />
    </div>
  </Page>
</template>
