<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  import { DEFAULT_HOME_PATH, DEFAULT_TENANT_ID } from '@vben/constants';

  import { message } from 'ant-design-vue';

  import { checkThirdPartyAuth } from '#/api/jsj-ai/api-v2';
  import { useAuthStore } from '#/store';

  const route = useRoute();
  const router = useRouter();
  const authStore = useAuthStore();

  const loading = ref(false);
  const error = ref('');
  const success = ref(false);

  /**
   * 处理第三方认证
   */
  async function handleThirdPartyAuth() {
    const token = route.query.token as string;
    console.log('获取到的token', token);

    if (!token) {
      error.value = '缺少认证token参数';
      return;
    }

    try {
      loading.value = true;
      error.value = '';

      // 调用第三方认证接口
      // API需要的是URL编码后的token，直接使用从URL获取的token
      const authResult = await checkThirdPartyAuth({
        auth: token,
      });

      if (!authResult.username || !authResult.password) {
        throw new Error('认证返回的用户信息不完整');
      }

      success.value = true;

      // 使用获取到的用户名和密码进行自动登录
      await performAutoLogin(authResult.username, authResult.password);
    } catch (error_: any) {
      console.error('第三方认证失败:', error_);
      error.value = error_.message || '认证失败，请重试';
      success.value = false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 执行自动登录
   */
  async function performAutoLogin(username: string, password: string) {
    try {
      // 构造登录参数
      const loginParams = {
        grantType: 'password' as const,
        password,
        tenantId: DEFAULT_TENANT_ID, // 使用默认租户ID
        username,
      };

      // 调用登录接口
      await authStore.authLogin(loginParams, async () => {
        message.success('登录成功');
        // 跳转到首页
        await router.replace(DEFAULT_HOME_PATH);
      });
    } catch (error_: any) {
      console.error('自动登录失败:', error_);
      error.value = `自动登录失败: ${error_.message || '未知错误'}`;
      success.value = false;
    }
  }

  /**
   * 重试认证
   */
  function retryAuth() {
    error.value = '';
    success.value = false;
    handleThirdPartyAuth();
  }

  /**
   * 返回登录页面
   */
  function goToLogin() {
    router.replace('/auth/login');
  }

  // 组件挂载时开始认证
  onMounted(() => {
    handleThirdPartyAuth();
  });
</script>

<template>
  <div class="third-party-auth-container">
    <div class="auth-card">
      <div class="auth-content">
        <div v-if="loading" class="loading-section">
          <a-spin size="large" />
          <p class="loading-text">正在验证认证信息...</p>
        </div>

        <div v-else-if="error" class="error-section">
          <a-result
            status="error"
            :title="error"
            sub-title="请检查您的认证链接是否正确，或联系管理员"
          >
            <template #extra>
              <a-button type="primary" @click="retryAuth">重试</a-button>
              <a-button @click="goToLogin">返回登录</a-button>
            </template>
          </a-result>
        </div>

        <div v-else-if="success" class="success-section">
          <a-result
            status="success"
            title="认证成功"
            sub-title="正在为您自动登录..."
          >
            <template #extra>
              <a-spin />
            </template>
          </a-result>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .third-party-auth-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .auth-card {
    width: 100%;
    max-width: 500px;
    overflow: hidden;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
  }

  .auth-header {
    padding: 24px;
    text-align: center;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .auth-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }

  .auth-content {
    padding: 40px 24px;
  }

  .loading-section {
    text-align: center;
  }

  .loading-text {
    margin-top: 16px;
    font-size: 16px;
    color: #666;
  }

  .error-section,
  .success-section {
    text-align: center;
  }

  :deep(.ant-result-title) {
    color: #333;
  }

  :deep(.ant-result-subtitle) {
    color: #666;
  }
</style>
