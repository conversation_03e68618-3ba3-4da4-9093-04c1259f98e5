<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { DownOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const router = useRouter();

interface VoucherItem {
  id: string;
  voucherNo: string;
  date: string;
  voucherType: string;
  summary: string;
  subjectCode: string;
  debitAmount: number;
  creditAmount: number;
}

interface VoucherData {
  items: VoucherItem[];
  total: number;
}

const route = useRoute();
const loading = ref(false);
const dateRange = ref<[dayjs.Dayjs, dayjs.Dayjs]>([
  dayjs('2020-01-04'),
  dayjs('2020-02-04'),
]);
const voucherNo = ref('');
const voucherData = ref<VoucherData>({
  items: [],
  total: 0,
});
const selectedRowKeys = ref<string[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);

// 获取凭证列表数据
const fetchVoucherList = async () => {
  try {
    loading.value = true;
    // 实际项目中应该调用真实的API
    // 这里模拟数据，实际使用时替换为真实API调用
    // const res = await requestClient.get('/jsj/voucher/list', {
    //   params: {
    //     startDate: dateRange.value[0].format('YYYY-MM-DD'),
    //     endDate: dateRange.value[1].format('YYYY-MM-DD'),
    //     voucherNo: voucherNo.value,
    //     page: currentPage.value,
    //     pageSize: pageSize.value
    //   }
    // });

    // 模拟数据
    const mockData: VoucherItem[] = [
      {
        id: 'PZ-1',
        voucherNo: 'PZ-1',
        date: '2021-01-02',
        voucherType: '普通凭证',
        summary: '报销销售人员的业务招待费',
        subjectCode: '560 销售费用-业务招待费',
        debitAmount: 3455,
        creditAmount: 3455,
      },
      {
        id: 'PZ-1',
        voucherNo: 'PZ-1',
        date: '2021-01-02',
        voucherType: '普通凭证',
        summary: '报销销售人员的业务招待费',
        subjectCode: '560 销售费用-业务招待费',
        debitAmount: 3455,
        creditAmount: 3455,
      },
    ];

    // 模拟多条数据
    voucherData.value = {
      items: [...mockData, ...mockData],
      total: 3,
    };
  } catch (error) {
    console.error('获取凭证列表失败', error);
    message.error('获取凭证列表失败');
  } finally {
    loading.value = false;
  }
};

// 查询按钮点击
const handleSearch = () => {
  currentPage.value = 1;
  fetchVoucherList();
};

const handleAdd = () => {
  router.push('/accountingVouchers/voucherDetailNew/add');
};

// 重置查询条件
const handleReset = () => {
  voucherNo.value = '';
  dateRange.value = [dayjs('2020-01-04'), dayjs('2020-02-04')];
  currentPage.value = 1;
  fetchVoucherList();
};

// 表格行选择变化
const onSelectChange = (selectedKeys: string[]) => {
  selectedRowKeys.value = selectedKeys;
};

// 页码变化
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchVoucherList();
};

// 查看凭证详情
const viewVoucherDetail = (id: string) => {
  // 实际项目中应该跳转到详情页或打开详情弹窗
  console.log('查看凭证详情', id);
};

// 编辑凭证
const editVoucher = (id: string) => {
  console.log('编辑凭证', id);
};

// 打印凭证
const printVoucher = (id: string) => {
  console.log('打印凭证', id);
};

// 冲销凭证
const reverseVoucher = (id: string) => {
  console.log('冲销凭证', id);
};

// 删除凭证
const deleteVoucher = (id: string) => {
  console.log('删除凭证', id);
};

// 复制凭证
const copyVoucher = (id: string) => {
  console.log('复制凭证', id);
};

// 注释凭证
const annotateVoucher = (id: string) => {
  console.log('注释凭证', id);
};

onMounted(() => {
  fetchVoucherList();
});
</script>

<template>
  <div class="voucher-detail-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <a-range-picker
        v-model:value="dateRange"
        format="YYYY-MM-DD"
        :placeholder="['开始日期', '结束日期']"
      />
      <a-input
        v-model:value="voucherNo"
        placeholder="凭证号"
        allow-clear
        style="width: 200px; margin-left: 8px"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <a-button type="primary" @click="handleSearch" style="margin-left: 8px">
        更多查询
      </a-button>
      <a-button type="primary" @click="handleAdd" style="margin-left: 8px">
        新增凭证
      </a-button>
      <a-dropdown style="margin-left: 8px">
        <a-button> 凭证导入 <DownOutlined /> </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item key="1">导入选项1</a-menu-item>
            <a-menu-item key="2">导入选项2</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-button style="margin-left: 8px">复制</a-button>
      <a-button style="margin-left: 8px">删除</a-button>
      <a-dropdown style="margin-left: 8px">
        <a-button> 选择排序方式 <DownOutlined /> </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item key="1">按日期排序</a-menu-item>
            <a-menu-item key="2">按凭证号排序</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-button style="margin-left: 8px">断号重排</a-button>
      <a-button style="margin-left: 8px">打印</a-button>
    </div>

    <!-- 表格区域 -->
    <a-table
      :data-source="voucherData.items"
      :loading="loading"
      :row-selection="{
        selectedRowKeys,
        onChange: (selectedRowKeys: any[], selectedRows: any[]) =>
          onSelectChange(selectedRowKeys as string[]),
        type: 'checkbox',
      }"
      :pagination="false"
      row-key="id"
    >
      <!-- 凭证号列 -->
      <a-table-column key="voucherNo" title="凭证号" data-index="voucherNo" />

      <!-- 日期列 -->
      <a-table-column key="date" title="日期" data-index="date" />

      <!-- 凭证类型列 -->
      <a-table-column
        key="voucherType"
        title="凭证类型"
        data-index="voucherType"
      />

      <!-- 摘要列 -->
      <a-table-column key="summary" title="摘要" data-index="summary" />

      <!-- 科目列 -->
      <a-table-column key="subjectCode" title="科目" data-index="subjectCode" />

      <!-- 借方金额列 -->
      <a-table-column key="debitAmount" title="借方金额" align="right">
        <template #default="{ record }">
          {{ record.debitAmount.toFixed(2) }}
        </template>
      </a-table-column>

      <!-- 贷方金额列 -->
      <a-table-column key="creditAmount" title="贷方金额" align="right">
        <template #default="{ record }">
          {{ record.creditAmount.toFixed(2) }}
        </template>
      </a-table-column>

      <!-- 操作列 -->
      <a-table-column key="action" title="操作" width="240px">
        <template #default="{ record }">
          <a-space>
            <a @click="viewVoucherDetail(record.id)">查看</a>
            <a @click="editVoucher(record.id)">修改</a>
            <a @click="printVoucher(record.id)">打印</a>
            <a @click="reverseVoucher(record.id)">冲销</a>
            <a @click="deleteVoucher(record.id)">删除</a>
            <a @click="copyVoucher(record.id)">复制</a>
            <a @click="annotateVoucher(record.id)">注释</a>
          </a-space>
        </template>
      </a-table-column>
    </a-table>

    <!-- 分页区域 -->
    <div class="pagination-area">
      <div>共 {{ voucherData.total }} 条凭证</div>
      <a-pagination
        v-model:current="currentPage"
        :total="voucherData.total"
        :page-size="pageSize"
        @change="handlePageChange"
        show-size-changer
        :page-size-options="['10', '20', '50', '100']"
        @update:page-size="(size: number) => (pageSize = size)"
        :show-total="
          (total: number, range: [number, number]) =>
            `共 ${total} 条 / ${Math.ceil(total / pageSize)} 页`
        "
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.voucher-detail-container {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
}

.search-area {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px 0;
}

.pagination-area {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.ant-table-row) {
  &.selected-row {
    background-color: #e6f7ff;
  }
}

:deep(.ant-table-row:nth-child(even)) {
  background-color: #fafafa;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #f5f5f5;
  font-weight: bold;
}
</style>
