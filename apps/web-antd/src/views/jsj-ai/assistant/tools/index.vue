<template>
  <div class="h-full p-6">
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">记账助手</h1>
        <p class="mt-2 text-gray-600">智能记账工具和辅助功能</p>
      </div>

      <!-- 工具卡片网格 -->
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <!-- 智能科目推荐 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <BulbOutlined class="h-8 w-8 text-yellow-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">智能科目推荐</h3>
              <p class="text-sm text-gray-500">基于AI分析推荐合适的会计科目</p>
            </div>
          </div>
          <div class="mt-4">
            <Button type="primary" block>开始使用</Button>
          </div>
        </div>

        <!-- 凭证模板 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <FileTextOutlined class="h-8 w-8 text-blue-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">凭证模板</h3>
              <p class="text-sm text-gray-500">常用记账凭证模板库</p>
            </div>
          </div>
          <div class="mt-4">
            <Button type="primary" block>查看模板</Button>
          </div>
        </div>

        <!-- 批量导入 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <UploadOutlined class="h-8 w-8 text-green-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">批量导入</h3>
              <p class="text-sm text-gray-500">批量导入银行流水和发票数据</p>
            </div>
          </div>
          <div class="mt-4">
            <Button type="primary" block>开始导入</Button>
          </div>
        </div>

        <!-- 自动对账 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <CheckCircleOutlined class="h-8 w-8 text-purple-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">自动对账</h3>
              <p class="text-sm text-gray-500">智能匹配银行流水和凭证</p>
            </div>
          </div>
          <div class="mt-4">
            <Button type="primary" block>开始对账</Button>
          </div>
        </div>

        <!-- 报表生成 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <BarChartOutlined class="h-8 w-8 text-red-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">报表生成</h3>
              <p class="text-sm text-gray-500">自动生成财务报表</p>
            </div>
          </div>
          <div class="mt-4">
            <Button type="primary" block>生成报表</Button>
          </div>
        </div>

        <!-- 税务计算 -->
        <div class="rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <CalculatorOutlined class="h-8 w-8 text-orange-500" />
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">税务计算</h3>
              <p class="text-sm text-gray-500">智能计算各类税费</p>
            </div>
          </div>
          <div class="mt-4">
            <Button type="primary" block>开始计算</Button>
          </div>
        </div>
      </div>

      <!-- 最近使用的工具 -->
      <div class="mt-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">最近使用</h2>
        <div class="space-y-3">
          <div
            v-for="tool in recentTools"
            :key="tool.id"
            class="flex items-center justify-between rounded-lg border border-gray-200 p-4"
          >
            <div class="flex items-center space-x-3">
              <component :is="tool.icon" class="h-5 w-5 text-gray-400" />
              <div>
                <h4 class="text-sm font-medium text-gray-900">{{ tool.name }}</h4>
                <p class="text-xs text-gray-500">{{ tool.lastUsed }}</p>
              </div>
            </div>
            <Button size="small">使用</Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Button } from 'ant-design-vue';
  import {
    BarChartOutlined,
    BulbOutlined,
    CalculatorOutlined,
    CheckCircleOutlined,
    FileTextOutlined,
    UploadOutlined,
  } from '@ant-design/icons-vue';

  // 最近使用的工具
  const recentTools = ref([
    {
      id: 1,
      name: '智能科目推荐',
      icon: BulbOutlined,
      lastUsed: '2024-01-15 14:30',
    },
    {
      id: 2,
      name: '批量导入',
      icon: UploadOutlined,
      lastUsed: '2024-01-15 10:15',
    },
    {
      id: 3,
      name: '自动对账',
      icon: CheckCircleOutlined,
      lastUsed: '2024-01-14 16:45',
    },
  ]);
</script>
