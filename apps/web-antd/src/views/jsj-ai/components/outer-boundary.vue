<script setup lang="ts">
  // 外边界组件，用于包装页面内容
</script>

<template>
  <div class="outer-boundary-scroll-container">
    <div class="outer-boundary">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
  .outer-boundary-scroll-container {
    width: 100%;
    height: calc(100vh - 88px);
    overflow: auto hidden;
  }

  .outer-boundary {
    box-sizing: border-box;
    width: 100%;
    min-width: 1200px;
    height: 100%;
    padding: 16px;
    overflow-y: auto;
  }
</style>
