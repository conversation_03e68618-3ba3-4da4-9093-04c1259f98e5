import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Select',
    fieldName: 'scene',
    label: '场景',
    componentProps: {
      placeholder: '请选择场景',
      allowClear: true,
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
      options: [], // 将在组件中动态设置
      class: 'w-[320px]',
    },
  },
  {
    component: 'Select',
    fieldName: 'type',
    label: '类型',
    componentProps: {
      placeholder: '请选择类型',
      options: [
        { label: '进项发票', value: '进项发票' },
        { label: '销项发票', value: '销项发票' },
        { label: '银行回单', value: '银行回单' },
      ],
    },
  },
];

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '公司名称',
    field: 'company_name',
    minWidth: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '场景',
    field: 'scene',
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '类型',
    field: 'type',
    minWidth: 100,
    showOverflow: 'tooltip',
  },
  {
    title: '状态',
    field: 'status',
    width: 80,
    showOverflow: 'tooltip',
    formatter: ({ row }) => {
      return row.status === 1 ? '启用' : '禁用';
    },
  },
  {
    title: '分录详情',
    field: 'detail',
    minWidth: 300,
    showOverflow: 'tooltip',
    formatter: ({ row }) => {
      if (!row.detail || !Array.isArray(row.detail)) {
        return '无分录';
      }
      return row.detail
        .map((item: any) => `${item.subject}(${item.direction}): ${item.source}`)
        .join('; ');
    },
  },
  {
    title: '创建时间',
    field: 'created_at',
    width: 160,
    showOverflow: 'tooltip',
    formatter: ({ cellValue }) => {
      if (!cellValue) return '';
      return new Date(cellValue).toLocaleString('zh-CN');
    },
  },
  {
    title: '更新时间',
    field: 'updated_at',
    width: 160,
    showOverflow: 'tooltip',
    formatter: ({ cellValue }) => {
      if (!cellValue) return '';
      return new Date(cellValue).toLocaleString('zh-CN');
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

// 模态框表单配置
export const modalSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: '_id',
    label: 'ID',
    dependencies: {
      show: () => false, // 隐藏字段
      triggerFields: [''],
    },
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择公司',
      allowClear: true,
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
      options: [], // 将在组件中动态设置
    },
    fieldName: 'company_name',
    label: '公司名称',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择场景',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
      options: [], // 将在组件中动态设置
      style: {
        width: '100%',
      },
    },
    fieldName: 'scene',
    label: '场景',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择类型',
      options: [], // 将在组件中动态设置
    },
    fieldName: 'type',
    label: '类型',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' },
      ],
    },
    defaultValue: '1',
    fieldName: 'status',
    label: '状态',
    rules: 'required',
  },
  {
    component: 'EntryDetailInput',
    componentProps: {
      selectedType: '', // 将在组件中动态设置
    },
    dependencies: {
      triggerFields: ['type'],
      componentProps: (values) => {
        return {
          selectedType: values.type || '',
        };
      },
    },
    fieldName: 'detail',
    label: '分录详情',
    rules: 'required',
  },
];
