<script setup lang="ts">
  import { computed, ref, watch } from 'vue';

  import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';

  interface Condition {
    name: string;
    value: string;
  }

  interface ConditionConfig {
    name: string;
    values?: string[];
  }

  interface Props {
    conditionOptions?: ConditionConfig[]; // 条件配置选项
    value?: Condition[];
  }

  interface Emits {
    (e: 'update:value', value: Condition[]): void;
    (e: 'change', value: Condition[]): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    conditionOptions: () => [],
    value: () => [],
  });

  const emit = defineEmits<Emits>();

  // 内部条件列表
  const conditions = ref<Condition[]>([]);

  // 条件名称选项
  const conditionNameOptions = computed(() => {
    return props.conditionOptions.map((item) => ({
      label: item.name,
      value: item.name,
    }));
  });

  // 获取指定条件名称的值选项
  const getConditionValueOptions = (conditionName: string) => {
    const config = props.conditionOptions.find(
      (item) => item.name === conditionName,
    );
    if (config && config.values && config.values.length > 0) {
      return config.values.map((value) => ({
        label: value,
        value,
      }));
    }
    return []; // 返回空数组而不是 null
  };

  // 检查是否有值选项
  const hasValueOptions = (conditionName: string) => {
    const config = props.conditionOptions.find(
      (item) => item.name === conditionName,
    );
    return config && config.values && config.values.length > 0;
  };

  // 处理条件名称变化
  const handleConditionNameChange = (
    index: number,
    newName: string | undefined,
  ) => {
    if (conditions.value[index] && newName) {
      conditions.value[index].name = newName;
      // 清空条件值，因为条件名称变了
      conditions.value[index].value = '';
      emitUpdate();
    }
  };

  // 初始化条件列表
  const initConditions = () => {
    conditions.value =
      props.value && props.value.length > 0
        ? [...props.value]
        : [{ name: '', value: '' }];
  };

  // 监听外部值变化
  watch(
    () => props.value,
    (newValue) => {
      if (newValue && newValue.length > 0) {
        conditions.value = [...newValue];
      } else {
        // 当外部传入空数组或undefined时，重置为一个空条件
        conditions.value = [{ name: '', value: '' }];
      }
    },
    { immediate: true },
  );

  // 发送更新事件的函数
  const emitUpdate = () => {
    emit('update:value', [...conditions.value]);
    emit('change', [...conditions.value]);
  };

  // 添加条件
  const addCondition = () => {
    conditions.value.push({ name: '', value: '' });
    emitUpdate();
  };

  // 删除条件
  const removeCondition = (index: number) => {
    if (conditions.value.length > 1) {
      conditions.value.splice(index, 1);
      emitUpdate();
    }
  };

  // 初始化
  initConditions();
</script>

<template>
  <div class="condition-input">
    <div
      v-for="(condition, index) in conditions"
      :key="index"
      class="condition-item"
    >
      <div class="condition-row">
        <div class="condition-field">
          <a-select
            v-model:value="condition.name"
            placeholder="请选择条件名称"
            class="condition-name"
            allow-clear
            show-search
            :options="conditionNameOptions"
            :filter-option="
              (input: string, option: any) =>
                option?.label?.toLowerCase().includes(input.toLowerCase())
            "
            @change="(value: any) => handleConditionNameChange(index, value)"
          />
        </div>
        <div class="condition-field">
          <!-- 根据条件名称决定显示下拉选择还是输入框 -->
          <a-select
            v-if="hasValueOptions(condition.name)"
            v-model:value="condition.value"
            placeholder="请选择条件值"
            class="condition-value"
            allow-clear
            :options="getConditionValueOptions(condition.name)"
            @change="emitUpdate"
          />
          <a-input
            v-else
            v-model:value="condition.value"
            placeholder="请输入条件值"
            class="condition-value"
            @input="emitUpdate"
          />
        </div>
        <div class="condition-actions">
          <a-button
            v-if="conditions.length > 1"
            type="text"
            danger
            size="small"
            @click="removeCondition(index)"
          >
            <template #icon>
              <DeleteOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </div>

    <div class="add-condition">
      <a-button type="dashed" block @click="addCondition">
        <template #icon>
          <PlusOutlined />
        </template>
        添加条件
      </a-button>
    </div>
  </div>
</template>

<style scoped>
  .condition-input {
    width: 100%;
  }

  .condition-item {
    margin-bottom: 8px;
  }

  .condition-row {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .condition-field {
    flex: 1;
  }

  .condition-name {
    width: 100%;
  }

  .condition-value {
    width: 100%;
  }

  .condition-actions {
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    width: 32px;
  }

  .add-condition {
    margin-top: 8px;
  }

  @media (max-width: 768px) {
    .condition-row {
      flex-direction: column;
      gap: 4px;
    }

    .condition-actions {
      align-self: flex-end;
      width: auto;
    }
  }
</style>
