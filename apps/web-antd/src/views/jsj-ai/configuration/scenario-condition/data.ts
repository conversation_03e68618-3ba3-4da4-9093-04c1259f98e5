import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Select',
    fieldName: 'scene',
    label: '场景',
    componentProps: {
      placeholder: '请选择场景',
      allowClear: true,
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
      options: [], // 将在组件中动态设置
      class: 'w-[320px]',
    },
  },
  {
    component: 'Select',
    fieldName: 'type',
    label: '类型',
    componentProps: {
      placeholder: '请选择类型',
      options: [
        { label: '进项发票', value: '进项发票' },
        { label: '销项发票', value: '销项发票' },
        { label: '银行回单', value: '银行回单' },
      ],
    },
  },
];

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '公司名称',
    field: 'company_name',
    minWidth: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '场景',
    field: 'scene',
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '类型',
    field: 'type',
    minWidth: 100,
    showOverflow: 'tooltip',
  },
  {
    title: '条件配置',
    field: 'condition',
    minWidth: 200,
    showOverflow: 'tooltip',
    formatter: ({ row }) => {
      if (!row.condition || !Array.isArray(row.condition)) {
        return '无条件';
      }
      return row.condition
        .map((item: any) => `${item.name}: ${item.value}`)
        .join('; ');
    },
  },
  {
    title: '创建时间',
    field: 'created_at',
    width: 160,
    showOverflow: 'tooltip',
    formatter: ({ cellValue }) => {
      if (!cellValue) return '';
      return new Date(cellValue).toLocaleString('zh-CN');
    },
  },
  {
    title: '更新时间',
    field: 'updated_at',
    width: 160,
    showOverflow: 'tooltip',
    formatter: ({ cellValue }) => {
      if (!cellValue) return '';
      return new Date(cellValue).toLocaleString('zh-CN');
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

// 模态框表单配置
export const modalSchema: FormSchemaGetter = () => [
  {
    label: 'ID',
    fieldName: '_id',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '公司名称',
    fieldName: 'company_name',
    component: 'Select',
    rules: 'required',
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请选择公司',
      allowClear: true,
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
      options: [], // 将在组件中动态设置
    },
  },
  {
    label: '场景',
    fieldName: 'scene',
    component: 'Input',
    rules: 'required',
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请输入场景',
      allowClear: true,
      style: {
        width: '100%',
      },
    },
  },
  {
    label: '类型',
    fieldName: 'type',
    component: 'Select',
    rules: 'required',
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请选择类型',
      allowClear: true,
      options: [], // 将在组件中动态设置
    },
  },
  {
    label: '条件配置',
    fieldName: 'condition',
    component: 'ConditionInput',
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请配置筛选条件',
    },
    help: '设置用于匹配数据的条件，支持多个条件组合',
  },
];
