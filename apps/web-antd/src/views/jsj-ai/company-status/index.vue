<script lang="ts" setup>
  import type { CompaniesQueryParams, CompanyData } from '#/api/jsj-ai/types';

  import {
    computed,
    nextTick,
    onBeforeUnmount,
    onMounted,
    ref,
    watch,
  } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import {
    EditOutlined,
    SearchOutlined,
    SyncOutlined,
  } from '@ant-design/icons-vue';
  import {
    Pagination as APagination,
    Button,
    Checkbox,
    Input,
    message,
    Modal,
    Select,
    Table,
    Tag,
    Tooltip,
  } from 'ant-design-vue';

  import {
    changeDataSource,
    fetchCompaniesList,
    syncCompanies,
  } from '#/api/jsj-ai/api-v2';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { usePagination } from '#/hooks/jsj-ai/usePagination';

  const userStore = useUserStore();
  const router = useRouter();
  const {
    fetchCompanyNames,
    handleMonthSelectChange,
    monthOptions,
    selectedCompany,
    selectedMonth,
  } = useCompanySelection();
  const loading = ref(false);
  const syncLoading = ref(false);
  const companies = ref<CompanyData[]>([]);
  const searchKeyword = ref('');
  const statusFilter = ref<string>('all');
  const tableContainer = ref<HTMLElement | null>(null);
  const tableScrollHeight = ref<number>(400);

  // 数据源切换相关状态
  const dataSourceSwitchLoading = ref<Record<string, boolean>>({});
  const dataSourceModalVisible = ref(false);
  const currentEditingCompany = ref<CompanyData | null>(null);
  const selectedDataSource = ref<string>('');
  const setAsDefault = ref(false);

  // 轮询相关变量
  let pollingTimer: NodeJS.Timeout | null = null;
  let pollingStartTime = 0;
  const POLLING_INTERVAL = 5000; // 3秒轮询一次
  const MAX_POLLING_DURATION = 30_000; // 最大轮询60秒

  // 使用分页 hook（包含滚动位置记忆）
  const { currentPage, pageSize, restoreScrollPosition } = usePagination({
    defaultPage: 1,
    defaultPageSize: 20,
    rememberScrollPosition: true,
    resetTriggers: [searchKeyword, statusFilter],
    scrollContainer: tableContainer,
    storageKey: 'company-status-pagination',
  });

  // 状态筛选选项
  const statusOptions = [
    { label: '全部', value: 'all' },
    { label: '销项发票已完成', value: 'output_invoice_done' },
    { label: '进项普票已完成', value: 'input_general_done' },
    { label: '进项专票已完成', value: 'input_vat_done' },
    { label: '银行回单已完成', value: 'bank_receipt_done' },
  ];

  // 过滤后的公司列表（带预处理的工具提示）
  const allFilteredCompanies = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return [];
    }

    let result = companies.value;

    // 关键词搜索 - 同时搜索客户名称和客户编号
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(
        (company) =>
          company.customerName.toLowerCase().includes(keyword) ||
          (company.customerCode &&
            company.customerCode.toLowerCase().includes(keyword)),
      );
    }

    // 状态筛选
    if (statusFilter.value !== 'all') {
      switch (statusFilter.value) {
        case 'bank_receipt_done': {
          result = result.filter(
            (company) => company.bank_receipt?.status === '3',
          );
          break;
        }
        case 'input_general_done': {
          result = result.filter(
            (company) => company.input_invoice_general?.status === '3',
          );
          break;
        }
        case 'input_vat_done': {
          result = result.filter(
            (company) => company.input_invoice_vat?.status === '3',
          );
          break;
        }
        case 'output_invoice_done': {
          result = result.filter(
            (company) => company.output_invoice?.status === '3',
          );
          break;
        }
        case 'top': {
          result = result.filter((company) => company.topFlag);
          break;
        }
      }
    }

    // 预处理工具提示内容，避免在渲染时计算
    return result.map((company) => ({
      ...company,
      _tooltips: {
        bank_receipt: getStatusTooltip(company.bank_receipt?.status || '0'),
        customs_declaration_form: getStatusTooltip(
          company.customs_declaration_form?.status || '0',
        ),
        input_invoice_general: getStatusTooltip(
          company.input_invoice_general?.status || '0',
        ),
        input_invoice_vat: getStatusTooltip(
          company.input_invoice_vat?.status || '0',
        ),
        output_invoice: getStatusTooltip(company.output_invoice?.status || '0'),
        payroll: getStatusTooltip(company.payroll?.status || '0'),
      },
    }));
  });

  // 当前页显示的公司列表
  const filteredCompanies = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return allFilteredCompanies.value.slice(start, end);
  });

  // 表格列定义
  const columns = [
    {
      dataIndex: 'customerName',
      ellipsis: {
        showTitle: false,
      },
      key: 'customerName',
      minWidth: 200,
      resizable: true,
      title: '公司名称',
      width: 280,
    },
    {
      align: 'center' as const,
      key: 'output_invoice',
      title: '销项发票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_general',
      title: '进项普票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_vat',
      title: '进项专票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'bank_receipt',
      title: '银行回单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'payroll',
      title: '薪酬',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'customs_declaration_form',
      title: '报关单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'data_source',
      title: '数据源',
      width: 100,
    },
    {
      align: 'center' as const,
      key: 'action',
      title: '操作',
      width: 120,
    },
  ];

  // 预定义所有状态的工具提示内容（避免运行时计算）
  const STATUS_TOOLTIPS = {
    '0': '❌ 数据未同步\n━━━━━━━━━━━━━━\n✗ 数据未同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 0/3 (0%)',
    '1': '🔄 数据已同步\n━━━━━━━━━━━━━━\n✓ 数据已同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 1/3 (33%)',
    '2': '📝 凭证已生成\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n○ 待回写凭证\n\n进度: 2/3 (67%)',
    '3': '✅ 凭证已回写\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n✓ 凭证已回写\n\n进度: 3/3 (100%)',
  } as const;

  // 获取状态工具提示信息（直接查表，无计算）
  const getStatusTooltip = (status: string) => {
    return (
      STATUS_TOOLTIPS[status as keyof typeof STATUS_TOOLTIPS] ||
      `状态: ${status || '未知'}\n请检查数据状态`
    );
  };

  // 统计数据
  const statistics = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return {
        bankReceiptDone: 0,
        inputCommonDone: 0,
        inputVatDone: 0,
        outputVoiceDone: 0,
        topCount: 0,
        total: 0,
      };
    }

    const total = allFilteredCompanies.value.length;
    const topCount = allFilteredCompanies.value.filter((c) => c.topFlag).length;

    // 根据新的数据结构计算统计
    const outputVoiceDone = allFilteredCompanies.value.filter(
      (c) => c.output_invoice?.status === '3',
    ).length;
    const inputCommonDone = allFilteredCompanies.value.filter(
      (c) => c.input_invoice_general?.status === '3',
    ).length;
    const inputVatDone = allFilteredCompanies.value.filter(
      (c) => c.input_invoice_vat?.status === '3',
    ).length;
    const bankReceiptDone = allFilteredCompanies.value.filter(
      (c) => c.bank_receipt?.status === '3',
    ).length;

    return {
      bankReceiptDone,
      inputCommonDone,
      inputVatDone,
      outputVoiceDone,
      topCount,
      total,
    };
  });

  // 获取公司列表数据
  async function fetchCompanies() {
    try {
      loading.value = true;
      const username = userStore.userInfo?.username;
      if (!username) {
        console.error('用户名未找到');
        return;
      }

      const params: CompaniesQueryParams = {
        account_name: username,
        month: selectedMonth.value,
        refresh: 0,
      };

      const response = await fetchCompaniesList(params);
      console.log('response', response);

      companies.value = response;

      // 数据加载完成后恢复滚动位置
      nextTick(() => {
        restoreScrollPosition();
      });
    } catch (error) {
      console.error('获取公司列表失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 处理月份选择变化
  const onMonthChange = (value: any) => {
    if (value && typeof value === 'string') {
      handleMonthSelectChange(value, () => {
        fetchCompanies();
      });
    }
  };

  // 处理AI工作台按钮点击
  const handleAIWorkspace = async (record: CompanyData) => {
    try {
      if (!record?.customerName) {
        message.error('无效的客户信息');
        return;
      }

      // 设置当前选择的公司到全局状态
      selectedCompany.value = record.customerName;
      console.log('已设置全局选中公司:', record.customerName);

      // 切换到工作台模式（会自动触发菜单更新）
      const { useMenuModeStore } = await import('#/store/modules/menu-mode');
      const menuModeStore = useMenuModeStore();

      // 切换模式
      await menuModeStore.setMenuMode('workspace');

      // 使用 router.replace 进行导航
      router.replace('/voucher/original');
    } catch (error) {
      console.error('切换到工作台模式失败:', error);
      message.error({
        content: '切换到工作台模式失败，请重试',
        duration: 2,
        key: 'switchMode',
      });
    }
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingTimer) {
      clearTimeout(pollingTimer);
      pollingTimer = null;
    }
  };

  // 开始轮询获取公司列表
  const startPolling = () => {
    pollingStartTime = Date.now();

    const poll = async () => {
      const currentTime = Date.now();
      const elapsedTime = currentTime - pollingStartTime;

      // 检查是否超过最大轮询时间
      if (elapsedTime >= MAX_POLLING_DURATION) {
        stopPolling();
        syncLoading.value = false;
        message.info('同步可能需要更长时间，请稍后手动刷新页面');
        return;
      }

      try {
        // 获取最新的公司列表
        await fetchCompanies();

        // 继续轮询
        pollingTimer = setTimeout(poll, POLLING_INTERVAL);
      } catch (error) {
        console.error('轮询获取公司列表失败:', error);
        // 即使失败也继续轮询，直到超时
        pollingTimer = setTimeout(poll, POLLING_INTERVAL);
      }
    };

    // 立即执行第一次轮询
    poll();
  };

  // 处理同步数据按钮点击
  const handleSyncData = async () => {
    try {
      syncLoading.value = true;

      const username = userStore.userInfo?.username;
      if (!username) {
        message.error('用户信息未找到，请重新登录');
        return;
      }

      await syncCompanies({
        month: selectedMonth.value,
        type: 'user_company_list',
        username,
      });

      message.success('同步请求已提交，请稍后');

      // 开始轮询获取最新数据
      startPolling();
    } catch (error: any) {
      console.error('同步数据失败:', error);
      message.error(error.message || '同步数据失败，请重试');
      syncLoading.value = false;
    }
  };

  // 打开数据源切换弹框
  const openDataSourceModal = (company: CompanyData) => {
    currentEditingCompany.value = company;
    selectedDataSource.value = company.data_source || 'jsj';
    setAsDefault.value = false;
    dataSourceModalVisible.value = true;
  };

  // 关闭数据源切换弹框
  const closeDataSourceModal = () => {
    dataSourceModalVisible.value = false;
    currentEditingCompany.value = null;
    selectedDataSource.value = '';
    setAsDefault.value = false;
  };

  // 确认数据源切换
  const confirmDataSourceChange = async () => {
    if (!currentEditingCompany.value) return;

    // 保存companyId，避免在finally块中currentEditingCompany被清空
    const companyId =
      currentEditingCompany.value.id ||
      currentEditingCompany.value.customerName;

    try {
      // 设置加载状态
      dataSourceSwitchLoading.value[companyId] = true;

      const params = {
        data_source: selectedDataSource.value,
        default: setAsDefault.value ? (1 as const) : (0 as const),
        id: companyId,
      };

      await changeDataSource(params);

      message.success('数据源切换成功');

      // 更新本地数据
      const companyIndex = companies.value.findIndex(
        (c) => (c.id || c.customerName) === companyId,
      );
      if (companyIndex !== -1 && companies.value[companyIndex]) {
        companies.value[companyIndex].data_source = selectedDataSource.value;
      }

      // 关闭弹框
      closeDataSourceModal();

      // 刷新数据列表
      await fetchCompanies();
    } catch (error: any) {
      console.error('切换数据源失败:', error);
      message.error(error.message || '切换数据源失败，请重试');
    } finally {
      // 清除加载状态
      dataSourceSwitchLoading.value[companyId] = false;
    }
  };

  function getProgressInfo(status: string) {
    switch (status) {
      case '0': {
        return {
          bgStroke: '#fee2e2',
          color: 'text-red-500',
          current: 0,
          shadowColor: 'rgba(239, 68, 68, 0.2)',
          stroke: '#ef4444',
          strokeGradient: 'url(#redGradient)',
        };
      }
      case '1': {
        return {
          bgStroke: '#fed7aa',
          color: 'text-orange-500',
          current: 1,
          shadowColor: 'rgba(249, 115, 22, 0.2)',
          stroke: '#f97316',
          strokeGradient: 'url(#orangeGradient)',
        };
      }
      case '2': {
        return {
          bgStroke: '#dbeafe',
          color: 'text-blue-500',
          current: 2,
          shadowColor: 'rgba(59, 130, 246, 0.2)',
          stroke: '#3b82f6',
          strokeGradient: 'url(#blueGradient)',
        };
      }
      case '3': {
        return {
          bgStroke: '#d1fae5',
          color: 'text-green-500',
          current: 3,
          shadowColor: 'rgba(16, 185, 129, 0.2)',
          stroke: '#10b981',
          strokeGradient: 'url(#greenGradient)',
        };
      }
      default: {
        return {
          bgStroke: '#f3f4f6',
          color: 'text-gray-400',
          current: 0,
          shadowColor: 'rgba(156, 163, 175, 0.1)',
          stroke: '#9ca3af',
          strokeGradient: 'url(#grayGradient)',
        };
      }
    }
  }

  // 监听全局月份状态变化
  watch(
    () => selectedMonth.value,
    () => {
      fetchCompanies();
    },
  );

  // 注意：分页状态的保存和重置逻辑已经在 usePagination hook 中处理

  // 计算表格滚动高度
  const calculateTableHeight = () => {
    nextTick(() => {
      if (tableContainer.value) {
        const containerHeight = tableContainer.value.clientHeight;
        const headerHeight = 32; // 表头高度
        tableScrollHeight.value = containerHeight - headerHeight;
      }
    });
  };

  // 添加tooltip控制变量
  const tooltipVisible = ref<{ [key: string]: boolean }>({});

  // 处理tooltip显示
  const handleTooltipVisibleChange = (visible: boolean, key: string) => {
    tooltipVisible.value[key] = visible;
  };

  // 清理所有tooltip
  const clearAllTooltips = () => {
    tooltipVisible.value = {};
  };

  // 组件卸载前清理
  onBeforeUnmount(() => {
    clearAllTooltips();
    stopPolling(); // 清理轮询定时器
  });

  onMounted(async () => {
    // 初始化公司列表
    await fetchCompanyNames();
    // 获取公司数据
    fetchCompanies();
    // 计算表格高度
    calculateTableHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateTableHeight);
  });
</script>

<template>
  <div class="company-status-page">
    <!-- 页面头部和控制面板 - 合并版 -->
    <div class="page-header-unified">
      <!-- 顶部行：标题、状态图例、统计信息 -->
      <div class="header-top-row">
        <div class="header-left">
          <h2 class="page-title">客户状态</h2>
          <div class="status-legend">
            <div class="legend-item">
              <div class="status-dot status-0"></div>
              <span>数据未同步</span>
            </div>
            <div class="legend-item">
              <div class="status-dot status-1"></div>
              <span>数据已同步</span>
            </div>
            <div class="legend-item">
              <div class="status-dot status-2"></div>
              <span>凭证已生成</span>
            </div>
            <div class="legend-item">
              <div class="status-dot status-3"></div>
              <span>凭证已回写</span>
            </div>
          </div>
        </div>
        <div class="header-stats-compact">
          <span class="stat-text">共 {{ statistics.total }} 家客户</span>
          <!-- <span class="stat-text">置顶 {{ statistics.topCount }} 家</span> -->
        </div>
      </div>

      <!-- 底部行：控制项 -->
      <div class="control-row">
        <div class="control-item">
          <span class="control-label-compact">月份</span>
          <Select
            :value="selectedMonth"
            placeholder="选择月份"
            size="small"
            :options="monthOptions"
            @change="onMonthChange"
            class="control-select-compact"
          />
        </div>

        <div class="control-item search-item">
          <Input
            v-model:value="searchKeyword"
            placeholder="搜索客户名称或编号"
            size="small"
            allow-clear
            class="search-input-compact"
          >
            <template #prefix>
              <SearchOutlined class="search-icon" />
            </template>
          </Input>
        </div>

        <div class="control-item">
          <span class="control-label-compact">筛选</span>
          <Select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            size="small"
            :options="statusOptions"
            class="control-select-compact"
          />
        </div>

        <div class="control-item">
          <Button
            type="primary"
            size="small"
            :loading="syncLoading"
            @click="handleSyncData"
            class="sync-button"
          >
            <template #icon>
              <SyncOutlined />
            </template>
            同步公司列表
          </Button>
        </div>
      </div>
    </div>

    <!-- 公司列表表格 - 紧凑版 -->
    <div class="table-card-compact">
      <div class="table-wrapper" ref="tableContainer">
        <Table
          :columns="columns"
          :data-source="filteredCompanies"
          :loading="loading"
          :pagination="false"
          row-key="customerName"
          size="small"
          class="custom-table"
          :scroll="{ y: tableScrollHeight }"
        >
          <!-- 表格单元格渲染 -->
          <template #bodyCell="{ column, record }">
            <!-- 公司名称列 -->
            <template v-if="column.key === 'customerName'">
              <div class="company-name-cell">
                <div class="company-tags">
                  <Tag v-if="record.topFlag" color="orange" size="small">
                    置顶
                  </Tag>
                  <Tag
                    v-if="record.company_type"
                    :color="
                      record.company_type === '小规模纳税人' ? 'blue' : 'green'
                    "
                    size="small"
                  >
                    {{ record.company_type === '小规模纳税人' ? '小' : '般' }}
                  </Tag>
                </div>
                <Tooltip
                  placement="topLeft"
                  :visible="tooltipVisible[`${record.customerName}-name`]"
                  @open-change="
                    (visible) =>
                      handleTooltipVisibleChange(
                        visible,
                        `${record.customerName}-name`,
                      )
                  "
                  :mouse-enter-delay="0.5"
                  :mouse-leave-delay="0.1"
                  :destroy-tooltip-on-hide="true"
                >
                  <template #title>
                    <div class="company-name-tooltip">
                      <div class="tooltip-content">
                        {{ record.customerName }}
                      </div>
                      <div v-if="record.customerCode" class="tooltip-code">
                        客户编号：{{ record.customerCode }}
                      </div>
                    </div>
                  </template>
                  <div class="company-name-text">
                    {{ record.customerName }}
                  </div>
                </Tooltip>
              </div>
            </template>

            <!-- 业务数据状态列 -->
            <template
              v-else-if="
                [
                  'output_invoice',
                  'input_invoice_general',
                  'input_invoice_vat',
                  'bank_receipt',
                  'payroll',
                  'customs_declaration_form',
                ].includes(column.key as string)
              "
            >
              <div class="flex justify-center">
                <Tooltip
                  placement="top"
                  :visible="
                    tooltipVisible[`${record.customerName}-${column.key}`]
                  "
                  @open-change="
                    (visible) =>
                      handleTooltipVisibleChange(
                        visible,
                        `${record.customerName}-${column.key}`,
                      )
                  "
                  :mouse-enter-delay="0.5"
                  :mouse-leave-delay="0.1"
                  :destroy-tooltip-on-hide="true"
                >
                  <template #title>
                    <div
                      class="tooltip-content"
                      v-html="
                        record._tooltips[column.key as string].replace(
                          /\n/g,
                          '<br>',
                        )
                      "
                    ></div>
                  </template>
                  <span
                    class="progress-ring-container inline-block cursor-pointer"
                    :data-status="record[column.key as string]?.status || '0'"
                  >
                    <svg class="progress-ring" viewBox="0 0 36 36">
                      <!-- 渐变定义 -->
                      <defs>
                        <linearGradient
                          id="redGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="100%"
                        >
                          <stop
                            offset="0%"
                            style="stop-color: #fca5a5; stop-opacity: 1"
                          />
                          <stop
                            offset="100%"
                            style="stop-color: #ef4444; stop-opacity: 1"
                          />
                        </linearGradient>
                        <linearGradient
                          id="orangeGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="100%"
                        >
                          <stop
                            offset="0%"
                            style="stop-color: #fdba74; stop-opacity: 1"
                          />
                          <stop
                            offset="100%"
                            style="stop-color: #f97316; stop-opacity: 1"
                          />
                        </linearGradient>
                        <linearGradient
                          id="blueGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="100%"
                        >
                          <stop
                            offset="0%"
                            style="stop-color: #93c5fd; stop-opacity: 1"
                          />
                          <stop
                            offset="100%"
                            style="stop-color: #3b82f6; stop-opacity: 1"
                          />
                        </linearGradient>
                        <linearGradient
                          id="greenGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="100%"
                        >
                          <stop
                            offset="0%"
                            style="stop-color: #6ee7b7; stop-opacity: 1"
                          />
                          <stop
                            offset="100%"
                            style="stop-color: #10b981; stop-opacity: 1"
                          />
                        </linearGradient>
                        <linearGradient
                          id="grayGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="100%"
                        >
                          <stop
                            offset="0%"
                            style="stop-color: #d1d5db; stop-opacity: 1"
                          />
                          <stop
                            offset="100%"
                            style="stop-color: #9ca3af; stop-opacity: 1"
                          />
                        </linearGradient>
                        <!-- 阴影滤镜 -->
                        <filter
                          id="progressShadow"
                          x="-50%"
                          y="-50%"
                          width="200%"
                          height="200%"
                        >
                          <feDropShadow
                            dx="0"
                            dy="1"
                            stdDeviation="2"
                            :flood-color="
                              getProgressInfo(
                                record[column.key as string]?.status || '0',
                              ).shadowColor
                            "
                          />
                        </filter>
                      </defs>

                      <!-- 背景圆环 -->
                      <circle
                        cx="18"
                        cy="18"
                        r="15"
                        :stroke="
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).bgStroke
                        "
                        stroke-width="2.5"
                        fill="none"
                        class="progress-bg"
                      />

                      <!-- 进度圆环 -->
                      <circle
                        v-if="
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).current > 0
                        "
                        cx="18"
                        cy="18"
                        r="15"
                        :stroke="
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).stroke
                        "
                        stroke-width="3"
                        fill="none"
                        stroke-linecap="round"
                        stroke-dasharray="94.2"
                        :stroke-dashoffset="
                          94.2 *
                          (1 -
                            getProgressInfo(
                              record[column.key as string]?.status || '0',
                            ).current /
                              3)
                        "
                        class="progress-circle"
                        style="
                          transform: rotate(-90deg);
                          transform-origin: 18px 18px;
                        "
                      />

                      <!-- 中心数字 -->
                      <text
                        x="18"
                        y="23"
                        text-anchor="middle"
                        font-size="11"
                        font-weight="600"
                        :fill="
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).stroke
                        "
                        class="progress-text"
                      >
                        {{
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).current
                        }}/3
                      </text>
                    </svg>
                  </span>
                </Tooltip>
              </div>
            </template>

            <!-- 数据源列 -->
            <template v-else-if="column.key === 'data_source'">
              <div class="data-source-cell">
                <div
                  v-if="
                    record.data_source_options &&
                    record.data_source_options.length > 1
                  "
                  class="data-source-switch"
                >
                  <Button
                    size="small"
                    @click="openDataSourceModal(record as CompanyData)"
                    class="data-source-button clickable"
                  >
                    <span class="data-source-value">
                      {{ record.data_source || 'jsj' }}
                    </span>
                    <EditOutlined class="edit-icon" />
                  </Button>
                </div>
                <div v-else class="data-source-text">
                  <span class="data-source-badge">
                    {{ record.data_source || 'jsj' }}
                  </span>
                </div>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <div class="action-buttons">
                <button
                  class="workspace-btn"
                  @click="handleAIWorkspace(record as CompanyData)"
                >
                  <EditOutlined class="btn-icon" />
                  <span>记账</span>
                </button>
              </div>
            </template>
          </template>

          <!-- 空数据 -->
          <template #emptyText>
            <div class="empty-state">
              <svg
                class="empty-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M8 14s1.5 2 4 2 4-2 4-2" />
                <line x1="9" y1="9" x2="9.01" y2="9" />
                <line x1="15" y1="9" x2="15.01" y2="9" />
              </svg>
              <div class="empty-text">暂无客户数据</div>
              <div class="empty-subtext">请检查筛选条件或联系管理员</div>
            </div>
          </template>
        </Table>
      </div>

      <!-- 固定分页器 - 右对齐 -->
      <div class="table-pagination-fixed">
        <div class="pagination-wrapper">
          <APagination
            v-model:current="currentPage"
            v-model:page-size="pageSize"
            :total="allFilteredCompanies.length"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :page-size-options="['10', '20', '50', '100']"
            size="small"
            :show-less-items="false"
          />
        </div>
      </div>
    </div>

    <!-- 数据源切换弹框 -->
    <Modal
      v-model:open="dataSourceModalVisible"
      title="切换数据源"
      :width="400"
      @ok="confirmDataSourceChange"
      @cancel="closeDataSourceModal"
      :confirm-loading="
        !!(
          currentEditingCompany &&
          dataSourceSwitchLoading[
            currentEditingCompany.id || currentEditingCompany.customerName
          ]
        )
      "
    >
      <div v-if="currentEditingCompany" class="data-source-modal-content">
        <div class="company-info">
          <h4>{{ currentEditingCompany.customerName }}</h4>
          <p class="current-source">
            当前数据源：{{ currentEditingCompany.data_source || 'jsj' }}
          </p>
        </div>

        <!-- 警告提示 -->
        <div class="warning-notice">
          <div class="warning-icon">⚠️</div>
          <div class="warning-content">
            <p class="warning-title">重要提示</p>
            <p class="warning-text">
              切换数据源将会清空该客户当前月份的所有数据，包括：
            </p>
            <ul class="warning-list">
              <li>发票数据</li>
              <li>银行回单数据</li>
              <li>凭证数据</li>
              <li>其他相关业务数据</li>
            </ul>
            <p class="warning-text">请确认您要继续此操作。</p>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">选择新数据源：</label>
          <Select
            v-model:value="selectedDataSource"
            placeholder="请选择数据源"
            style="width: 100%"
            :options="
              (currentEditingCompany.data_source_options || []).map(
                (source) => ({ label: source, value: source }),
              )
            "
          />
        </div>

        <div class="form-item">
          <Checkbox v-model:checked="setAsDefault">
            同时设置为默认数据源
          </Checkbox>
          <p class="form-help">勾选后，该数据源将成为此客户的默认数据源</p>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
  /* 进度圆环动画 */
  @keyframes progress-rotate {
    0% {
      stroke-dasharray: 0, 94.25;
    }

    100% {
      stroke-dasharray: var(--progress-length), 94.25;
    }
  }

  @keyframes success-pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1.1);
    }

    50% {
      opacity: 0.9;
      transform: scale(1.15);
    }
  }

  @keyframes error-shake {
    0%,
    100% {
      transform: translateX(0);
    }

    25% {
      transform: translateX(-2px);
    }

    75% {
      transform: translateX(2px);
    }
  }

  /* 页面整体样式 - 超紧凑版 */
  .company-status-page {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 88px);
    padding: 8px;
    overflow: hidden;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif;
    background: #f8fafc;
  }

  /* 页面头部和控制面板 - 合并统一版 */
  .page-header-unified {
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    gap: 16px;
    padding: 16px 20px;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow:
      0 1px 3px rgb(0 0 0 / 8%),
      0 1px 2px rgb(0 0 0 / 6%);
  }

  .header-top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header-left {
    display: flex;
    gap: 24px;
    align-items: center;
  }

  .page-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
  }

  .status-legend {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .legend-item {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .legend-item:hover {
    background: rgb(59 130 246 / 5%);
    transform: translateY(-1px);
  }

  .legend-item span {
    font-size: 11px;
    font-weight: 500;
    color: #6b7280;
    transition: color 0.2s ease;
  }

  .legend-item:hover span {
    font-weight: 600;
    color: #374151;
  }

  .status-dot {
    flex-shrink: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .legend-item:hover .status-dot {
    box-shadow: 0 3px 12px rgb(0 0 0 / 20%);
    transform: scale(1.3);
  }

  /* 状态0：数据未同步 - 红色 */
  .status-0 {
    background: linear-gradient(135deg, #fca5a5, #ef4444);
    border: 1px solid #fee2e2;
  }

  /* 状态1：数据已同步 - 橙色 */
  .status-1 {
    background: linear-gradient(135deg, #fdba74, #f97316);
    border: 1px solid #fed7aa;
  }

  /* 状态2：凭证已生成 - 蓝色 */
  .status-2 {
    background: linear-gradient(135deg, #93c5fd, #3b82f6);
    border: 1px solid #dbeafe;
  }

  /* 状态3：凭证已回写 - 绿色 */
  .status-3 {
    background: linear-gradient(135deg, #6ee7b7, #10b981);
    border: 1px solid #d1fae5;
  }

  .header-stats-compact {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .stat-text {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
  }

  .control-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
  }

  .control-item {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  .search-item {
    flex: 1;
    min-width: 280px;
  }

  .control-label-compact {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    white-space: nowrap;
  }

  .control-select-compact {
    width: 130px;
  }

  :deep(.control-select-compact .ant-select-selector) {
    display: flex !important;
    align-items: center !important;
    height: 28px !important;
    padding: 0 8px !important;
    background: white !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%) !important;
    transition: all 0.2s ease !important;
  }

  :deep(.control-select-compact .ant-select-selection-item) {
    flex: 1 !important;
    line-height: 26px !important;
    text-align: center !important;
  }

  :deep(.control-select-compact .ant-select-selection-placeholder) {
    flex: 1 !important;
    line-height: 26px !important;
    text-align: center !important;
  }

  :deep(.control-select-compact:hover .ant-select-selector) {
    border-color: #cbd5e1 !important;
    box-shadow: 0 1px 3px rgb(0 0 0 / 8%) !important;
  }

  :deep(.control-select-compact.ant-select-focused .ant-select-selector) {
    border-color: #3b82f6 !important;
    box-shadow:
      0 0 0 3px rgb(59 130 246 / 12%) !important,
      0 1px 3px rgb(0 0 0 / 8%) !important;
  }

  .search-input-compact {
    flex: 1;
  }

  :deep(.search-input-compact.ant-input-affix-wrapper) {
    height: 28px !important;
    padding: 0 8px !important;
    background: white !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%) !important;
    transition: all 0.2s ease !important;
  }

  :deep(.search-input-compact.ant-input-affix-wrapper:hover) {
    border-color: #94a3b8 !important;
    box-shadow: 0 2px 8px rgb(0 0 0 / 12%) !important;
    transform: translateY(-1px) !important;
  }

  :deep(.search-input-compact.ant-input-affix-wrapper-focused) {
    border-color: #3b82f6 !important;
    box-shadow:
      0 0 0 3px rgb(59 130 246 / 12%) !important,
      0 1px 3px rgb(0 0 0 / 8%) !important;
  }

  :deep(.search-input-compact .ant-input) {
    height: 100% !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    outline: none !important;
    box-shadow: none !important;
  }

  :deep(.search-input-compact .ant-input:hover) {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  :deep(.search-input-compact .ant-input:focus) {
    background: transparent !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
  }

  :deep(.search-input-compact .ant-input-prefix) {
    margin-right: 6px !important;
  }

  .search-icon {
    font-size: 14px !important;
    color: #64748b !important;
    transition: color 0.2s ease !important;
  }

  :deep(.search-input-compact.ant-input-affix-wrapper-focused .search-icon) {
    color: #3b82f6 !important;
  }

  /* 表格卡片样式 - 美观版 */
  .table-card-compact {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow:
      0 1px 3px rgb(0 0 0 / 8%),
      0 1px 2px rgb(0 0 0 / 6%);
  }

  .table-wrapper {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
  }

  .table-pagination-fixed {
    display: flex;
    flex-shrink: 0;
    justify-content: flex-end;
    padding: 8px 16px;
    background: linear-gradient(to right, #fafbfc, #f8fafc);
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 -1px 3px rgb(0 0 0 / 5%);
  }

  .pagination-wrapper {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
  }

  .pagination-info {
    display: none;
  }

  .total-info {
    display: none;
  }

  /* 自定义表格样式 - 动态高度版 */
  :deep(.table-wrapper .ant-table) {
    background: transparent;
  }

  :deep(.table-wrapper .ant-table-thead > tr > th) {
    position: sticky;
    top: 0;
    z-index: 10;
    height: 32px;
    padding: 6px 4px;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    text-align: center;
    background: #f9fafb !important;
    border-bottom: 1px solid #e5e7eb;
  }

  :deep(.table-wrapper .ant-table-tbody > tr > td) {
    height: 30px;
    padding: 4px 6px;
    background: white;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.15s ease;
  }

  :deep(.table-wrapper .ant-table-tbody > tr:hover > td) {
    background: #f8fafc;
  }

  :deep(.table-wrapper .ant-table-tbody > tr:nth-child(even) > td) {
    background: #fafbfc;
  }

  :deep(.table-wrapper .ant-table-tbody > tr:nth-child(even):hover > td) {
    background: #f1f5f9;
  }

  /* 确保最后一行完全可见 */
  :deep(.table-wrapper .ant-table-tbody > tr:last-child > td) {
    border-bottom: 1px solid #f3f4f6;
  }

  /* 操作按钮样式 - 紧凑版 */
  .action-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
  }

  .workspace-btn {
    position: relative;
    display: flex;
    gap: 3px;
    align-items: center;
    padding: 2px 8px;
    overflow: hidden;
    font-size: 11px;
    font-weight: 500;
    color: white;
    cursor: pointer;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border: none;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgb(59 130 246 / 20%);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .workspace-btn::before {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    content: '';
    background: linear-gradient(
      90deg,
      transparent,
      rgb(255 255 255 / 20%),
      transparent
    );
    transition: left 0.5s ease;
  }

  .workspace-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    box-shadow: 0 4px 12px rgb(59 130 246 / 30%);
    transform: translateY(-2px);
  }

  .workspace-btn:hover::before {
    left: 100%;
  }

  .workspace-btn:active {
    box-shadow: 0 2px 6px rgb(59 130 246 / 25%);
    transform: translateY(-1px);
  }

  .workspace-btn:focus {
    outline: 2px solid #93c5fd;
    outline-offset: 2px;
  }

  .btn-icon {
    width: 10px;
    height: 10px;
  }

  /* 数据源列样式 */
  .data-source-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .data-source-switch {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .data-source-select {
    width: 100% !important;
    min-width: 60px !important;
  }

  .data-source-text {
    font-size: 12px;
    color: #374151;
    text-align: center;
  }

  .data-source-badge {
    display: inline-block;
    padding: 2px 8px;
    font-size: 11px;
    color: #6b7280;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
  }

  .data-source-button {
    display: flex !important;
    gap: 4px !important;
    align-items: center !important;
    height: 26px !important;
    padding: 4px 8px !important;
    font-size: 11px !important;
    color: #3b82f6 !important;
    background: #eff6ff !important;
    border: 1px solid #bfdbfe !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
  }

  .data-source-button:hover {
    color: #1d4ed8 !important;
    background: #dbeafe !important;
    border-color: #93c5fd !important;
    box-shadow: 0 2px 4px rgb(59 130 246 / 15%) !important;
    transform: translateY(-1px) !important;
  }

  .data-source-button:active {
    box-shadow: 0 1px 2px rgb(59 130 246 / 10%) !important;
    transform: translateY(0) !important;
  }

  .data-source-value {
    font-weight: 500;
  }

  .edit-icon {
    font-size: 10px !important;
    opacity: 0.7;
  }

  .data-source-button:hover .edit-icon {
    opacity: 1;
  }

  :deep(.data-source-select .ant-select-selector) {
    height: 24px !important;
    padding: 0 6px !important;
    font-size: 11px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
  }

  :deep(.data-source-select .ant-select-selection-item) {
    font-size: 11px !important;
    line-height: 22px !important;
  }

  :deep(.data-source-select .ant-select-arrow) {
    font-size: 10px !important;
  }

  /* 数据源切换弹框样式 */
  .data-source-modal-content {
    padding: 16px 0;
  }

  .company-info {
    padding: 12px;
    margin-bottom: 20px;
    background: #f8fafc;
    border-radius: 6px;
  }

  .company-info h4 {
    margin: 0 0 8px;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
  }

  .current-source {
    margin: 0;
    font-size: 12px;
    color: #6b7280;
  }

  /* 警告提示样式 */
  .warning-notice {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    margin-bottom: 20px;
    background: #fef3cd;
    border: 1px solid #fbbf24;
    border-radius: 6px;
  }

  .warning-icon {
    flex-shrink: 0;
    margin-top: 2px;
    margin-right: 8px;
    font-size: 16px;
  }

  .warning-content {
    flex: 1;
  }

  .warning-title {
    margin: 0 0 8px;
    font-size: 13px;
    font-weight: 600;
    color: #92400e;
  }

  .warning-text {
    margin: 0 0 8px;
    font-size: 12px;
    line-height: 1.4;
    color: #92400e;
  }

  .warning-text:last-child {
    margin-bottom: 0;
  }

  .warning-list {
    padding-left: 16px;
    margin: 8px 0;
    font-size: 12px;
    color: #92400e;
  }

  .warning-list li {
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .warning-list li:last-child {
    margin-bottom: 0;
  }

  .form-item {
    margin-bottom: 16px;
  }

  .form-label {
    display: block;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
    color: #374151;
  }

  .form-help {
    margin: 8px 0 0;
    font-size: 12px;
    line-height: 1.4;
    color: #6b7280;
  }

  /* 空状态样式 - 紧凑版 */
  .empty-state {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    padding: 32px 16px;
    color: #6b7280;
  }

  .empty-icon {
    width: 48px;
    height: 48px;
    color: #d1d5db;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
  }

  .empty-subtext {
    font-size: 12px;
    color: #6b7280;
  }

  /* 优化 Tooltip 样式 */
  :deep(.ant-tooltip) {
    z-index: 1000;
    transition:
      opacity 0.2s ease-in-out,
      transform 0.2s ease-in-out;
  }

  :deep(.ant-tooltip-inner) {
    position: relative;
    max-width: 280px;
    padding: 16px 20px;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.6;
    color: #f9fafb;
    letter-spacing: 0.025em;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border: 1px solid #4b5563;
    border-radius: 10px;
    box-shadow:
      0 10px 25px -5px rgb(0 0 0 / 10%),
      0 10px 10px -5px rgb(0 0 0 / 4%);
  }

  :deep(.tooltip-content) {
    word-break: break-word;
    white-space: normal;
  }

  :deep(.tooltip-content br) {
    display: block;
    margin: 4px 0;
  }

  :deep(.ant-tooltip-arrow::before) {
    background-color: #1f2937;
  }

  :deep(svg) {
    pointer-events: auto;
  }

  /* 进度圆环样式优化 - 增强版 */
  .progress-ring-container {
    position: relative;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .progress-ring-container:hover {
    z-index: 10;
    transform: scale(1.15);
  }

  .progress-ring-container:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .progress-ring {
    width: 32px;
    height: 32px;
    filter: drop-shadow(0 2px 4px rgb(0 0 0 / 10%));
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .progress-ring-container:hover .progress-ring {
    filter: drop-shadow(0 6px 12px rgb(0 0 0 / 20%));
  }

  .progress-bg {
    transition: all 0.3s ease;
  }

  .progress-circle {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  .progress-text {
    font-family:
      'SF Pro Display',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif;
    transition: all 0.3s ease;
  }

  .progress-ring-container:hover .progress-text {
    font-weight: 700;
  }

  /* 不同状态的特殊效果 */
  .progress-ring-container[data-status='3']:hover {
    animation: success-pulse 1.5s ease-in-out infinite;
  }

  .progress-ring-container[data-status='0'] .progress-ring {
    opacity: 0.8;
  }

  .progress-ring-container[data-status='0']:hover .progress-ring {
    opacity: 1;
    animation: error-shake 0.5s ease-in-out;
  }

  /* 公司名称列样式 */
  .company-name-cell {
    display: flex;
    gap: 6px;
    align-items: center;
    width: 100%;
    min-height: 22px;
  }

  .company-tags {
    display: flex;
    flex-shrink: 0;
    gap: 3px;
    align-items: center;
  }

  .company-name-text {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .company-name-text:hover {
    color: #3b82f6;
  }

  /* 公司名称 Tooltip 样式 */
  .company-name-tooltip {
    max-width: 400px;
  }

  .company-name-tooltip .tooltip-title {
    padding-bottom: 4px;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 600;
    color: #e5e7eb;
    border-bottom: 1px solid #4b5563;
  }

  .company-name-tooltip .tooltip-content {
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    color: #f9fafb;
    word-break: break-all;
    white-space: normal;
  }

  .company-name-tooltip .tooltip-code {
    font-size: 12px;
    font-style: italic;
    color: #d1d5db;
  }

  /* 分页样式优化 - 美观版 */
  :deep(.table-pagination-fixed .ant-pagination) {
    display: flex !important;
    gap: 6px !important; /* 设置页码之间的间距 */
    align-items: center !important;
    padding: 0;
    margin: 0;
  }

  /* 为所有分页元素设置统一的右边距来创建间距 */
  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-item),
  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-prev),
  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-next),
  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-jump-prev),
  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-jump-next) {
    margin-right: 6px !important;
    margin-left: 0 !important;
  }

  /* 最后一个元素不需要右边距 */
  :deep(
    .table-pagination-fixed .ant-pagination .ant-pagination-item:last-of-type
  ),
  :deep(
    .table-pagination-fixed .ant-pagination .ant-pagination-next:last-child
  ) {
    margin-right: 0 !important;
  }

  /* 特别处理分页选项区域的间距 */
  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-options) {
    margin-left: 16px !important; /* 与页码区域保持更大间距 */
  }

  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-item) {
    min-width: 28px;
    height: 28px;
    font-size: 12px;
    font-weight: 500;
    line-height: 26px;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
    transition: all 0.2s ease;
  }

  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-item:hover) {
    background: #f0f9ff;
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgb(59 130 246 / 15%);
    transform: translateY(-1px);
  }

  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-item-active) {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgb(59 130 246 / 25%);
  }

  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-item-active a) {
    font-weight: 600;
    color: white !important;
  }

  /* 修复已选中页码悬浮时的文字显示问题 */
  :deep(
    .table-pagination-fixed .ant-pagination .ant-pagination-item-active:hover
  ) {
    background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
    border-color: #2563eb !important;
    box-shadow: 0 3px 12px rgb(59 130 246 / 35%) !important;
    transform: translateY(-1px) !important;
  }

  :deep(
    .table-pagination-fixed .ant-pagination .ant-pagination-item-active:hover a
  ) {
    font-weight: 700 !important;
    color: white !important;
    text-shadow: 0 1px 2px rgb(0 0 0 / 20%) !important;
  }

  :deep(
    .table-pagination-fixed .ant-pagination .ant-pagination-prev,
    .table-pagination-fixed .ant-pagination .ant-pagination-next
  ) {
    min-width: 28px;
    height: 28px;
    line-height: 26px;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
    transition: all 0.2s ease;
  }

  :deep(
    .table-pagination-fixed .ant-pagination .ant-pagination-prev:hover,
    .table-pagination-fixed .ant-pagination .ant-pagination-next:hover
  ) {
    background: #f0f9ff;
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgb(59 130 246 / 15%);
    transform: translateY(-1px);
  }

  :deep(
    .table-pagination-fixed .ant-pagination .ant-pagination-options .ant-select
  ) {
    margin-right: 8px;
  }

  :deep(
    .table-pagination-fixed
      .ant-pagination
      .ant-pagination-options
      .ant-select-selector
  ) {
    border-color: #d1d5db !important;
    border-radius: 6px !important;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%) !important;
  }

  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-total-text) {
    margin-right: 12px;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
  }

  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-jump-prev),
  :deep(.table-pagination-fixed .ant-pagination .ant-pagination-jump-next) {
    min-width: 28px;
    height: 28px;
    line-height: 26px;
  }

  /* 同步按钮样式 */
  .sync-button {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    border: none !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 4px rgb(16 185 129 / 20%) !important;
    transition: all 0.2s ease !important;
  }

  .sync-button:hover {
    background: linear-gradient(135deg, #059669, #047857) !important;
    box-shadow: 0 4px 8px rgb(16 185 129 / 30%) !important;
    transform: translateY(-1px) !important;
  }

  .sync-button:active {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgb(16 185 129 / 20%) !important;
    transform: translateY(0) !important;
  }
</style>
