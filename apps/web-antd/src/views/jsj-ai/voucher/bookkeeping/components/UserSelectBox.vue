<script setup lang="ts">
  import { computed, defineEmits, defineProps, ref, watch } from 'vue';

  import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';

  const { btntext, options, type } = defineProps<{
    btntext: string;
    options: any;
    type: string;
  }>();
  const emits = defineEmits([
    'selectChange',
    'newlyAddedClick',
    'addAuxiliaryClick',
  ]);
  const seachVal = ref<string>('');
  const inputRef = ref<any>('');
  const filteredOptions = ref<any[]>([]);

  // 计算属性：安全地获取选项列表
  const optionsList = computed(() => {
    // 处理不同的数据格式
    if (!options) return [];

    // 如果 options 是 ref，获取其 value
    const rawOptions = options.value || options;

    // 确保返回数组
    return Array.isArray(rawOptions) ? rawOptions : [];
  });
  const itmclick = (itm: any) => {
    emits('selectChange', itm, type);
  };
  const inputClick = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
  };
  // 新增摘要/科目
  const newlyAddedClick = (e: Event, type: 'auxiliary' | 'subject') => {
    e.preventDefault();
    e.stopPropagation();
    if (type === 'subject') {
      emits('newlyAddedClick');
    } else {
      emits('addAuxiliaryClick');
    }
  };
  watch(seachVal, (newVal, oldVal) => {
    console.log(newVal, oldVal);
    if (!newVal) {
      filteredOptions.value = [];
      return;
    }

    const arr = optionsList.value.filter((v: any) => {
      const text = v.text || v.label || '';
      const id = v.id || v.value || '';
      return text.includes(newVal) || id.includes(newVal);
    });
    filteredOptions.value = arr;
  });
</script>
<template>
  <div class="box">
    <a-input
      style="width: 100%; height: 30px"
      v-model:value="seachVal"
      ref="inputRef"
      placeholder=""
      class="mb-2 text-left"
      @click="inputClick"
    />
    <div v-if="optionsList.length > 0" class="listcont">
      <template v-if="seachVal === ''">
        <div
          v-for="itm in optionsList"
          :key="itm.text || itm.label"
          class="itm"
          @click="itmclick(itm)"
        >
          {{ itm.text || itm.label }}
        </div>
      </template>
      <template v-else>
        <div
          v-for="itm in filteredOptions"
          :key="itm.text || itm.label"
          class="itm"
          @click="itmclick(itm)"
        >
          {{ itm.text || itm.label }}
        </div>
      </template>
    </div>
    <div v-if="optionsList.length === 0" class="btn loading text-center">
      加载中
      <LoadingOutlined />
    </div>
    <div v-if="optionsList.length > 0" class="btn-group">
      <div class="btn text-center" @click="newlyAddedClick($event, 'subject')">
        <PlusOutlined />
        {{ btntext }}
      </div>
      <div
        class="btn text-center"
        @click="newlyAddedClick($event, 'auxiliary')"
      >
        <PlusOutlined />
        新增辅助
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
  .box {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 4px;
    margin: 0;
    font-size: 14px;
    color: rgb(50 54 57 / 88%);
    list-style: none;
    background-color: hsl(0deg 0% 100%);
    border-radius: 8px;
    outline: none;
    box-shadow:
      0 6px 16px 0 rgb(0 0 0 / 8%),
      0 3px 6px -4px rgb(0 0 0 / 12%),
      0 9px 28px 8px rgb(0 0 0 / 5%);

    .listcont {
      flex: 1;
      overflow: auto;
    }

    .loading {
      margin-top: 20px;
      margin-bottom: 20px;
    }

    .itm,
    .btn {
      position: relative;
      box-sizing: border-box;
      display: block;
      min-height: 32px;
      padding: 5px 12px;
      font-size: 14px;
      color: rgb(50 54 57 / 88%);
      cursor: pointer;
      border-radius: 4px;
      transition: background 0.3s ease;
    }

    .btn-group {
      display: flex;
      border-top: 1px solid #f0f0f0;

      .btn {
        flex: 1;
        padding: 8px;
        background-color: transparent;
        border-radius: 0;

        &:hover {
          background-color: rgb(50 54 57 / 4%);
        }

        &:first-child {
          border-right: 1px solid #f0f0f0;
        }
      }
    }

    .itm:hover {
      background-color: rgb(50 54 57 / 4%);
    }
  }
</style>
