<script lang="ts" setup>
  import { reactive, ref } from 'vue';

  import { message } from 'ant-design-vue';

  import { addAuxiliarySave } from '#/api/account-book/bookkeeping/index';

  import emitter from './usermitt';

  defineOptions({
    name: 'AddAuxiliaryPop',
  });

  const props = withDefaults(defineProps<Props>(), {
    auxiliaryType: 'customer',
  });

  const emits = defineEmits(['refresh']);

  interface Props {
    auxiliaryType?:
      | 'customer'
      | 'department'
      | 'employee'
      | 'inventory'
      | 'project'
      | 'supplier';
  }

  const visible = ref(false);
  const formRef = ref<any>(null);

  const formState = reactive({
    code: '999999', // 辅助核算编码，默认999999
    name: '', // 辅助核算名称
    type: props.auxiliaryType, // 辅助核算类型
  });

  // 辅助核算类型选项
  const auxiliaryTypeOptions = [
    { label: '客户', value: 'customer' },
    { label: '供应商', value: 'supplier' },
    { label: '员工', value: 'employee' },
    { label: '部门', value: 'department' },
    { label: '项目', value: 'project' },
    { label: '存货', value: 'inventory' },
  ];

  // 重置表单
  const resetForm = () => {
    formState.code = '999999';
    formState.name = '';
    formState.type = props.auxiliaryType;
  };

  const open = () => {
    resetForm();
    visible.value = true;
  };

  const handleOk = async () => {
    try {
      await formRef.value.validate();

      const todata = {
        code: formState.code,
        name: formState.name,
        type: formState.type,
      };

      const res = await addAuxiliarySave(todata);

      if (res.returnCode === '200') {
        message.success('新增辅助核算成功');
        // 通知凭证页面刷新辅助核算数据
        emitter.emit('account_voucher_auxiliary_added', res.data);
        // 通知父组件刷新
        emits('refresh');
        visible.value = false;
      } else {
        message.warning(res.returnMsg || '保存失败');
      }
    } catch (error) {
      console.error('新增辅助核算失败:', error);
      message.error('新增辅助核算失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };

  defineExpose({
    open,
  });
</script>

<template>
  <a-modal
    v-model:open="visible"
    title="新增辅助项目"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div>
      <a-form
        :model="formState"
        autocomplete="off"
        ref="formRef"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="*编码"
          name="code"
          :rules="[{ required: true, message: '请输入编码' }]"
        >
          <a-input v-model:value="formState.code" placeholder="默认999999" />
        </a-form-item>
        <a-form-item
          label="*名称"
          name="name"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model:value="formState.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item
          label="*辅助类别"
          name="type"
          :rules="[{ required: true, message: '请选择辅助类别' }]"
        >
          <a-select
            v-model:value="formState.type"
            placeholder="请选择辅助类别"
            :options="auxiliaryTypeOptions"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
  .btn {
    text-align: center;
  }

  .reminder {
    font-size: 10px;
    color: red;
  }
</style>
