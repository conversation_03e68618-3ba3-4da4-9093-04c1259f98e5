<script lang="ts" setup>
  import { reactive, ref } from 'vue';

  import { useVbenModal } from '@vben/common-ui';

  import { message } from 'ant-design-vue';

  import { addSubjectSave } from '#/api/account-book/bookkeeping/index';
  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  import emitter from './usermitt';

  defineOptions({
    name: 'AddSubjectPop',
  });

  const emits = defineEmits(['refresh']);

  const [Modal, ModalApi] = useVbenModal({
    onOpenChange: (type) => {
      if (type) {
        // 重置表单
        resetForm();
        // 新增科目时，设置默认编码
        formState.code = '404';
      }
    },
  });

  const formRef = ref<any>(null);
  const { pureSubjectOptions } = useAccountSubjects();

  const formState = reactive({
    code: '404', // 科目编码，默认404
    name: '', // 科目名称
    parentId: '', // 上级科目id
  });

  // 重置表单
  const resetForm = () => {
    formState.code = '404';
    formState.name = '';
    formState.parentId = '';
  };
  ModalApi.onConfirm = async () => {
    try {
      await formRef.value.validate();

      const todata = {
        code: formState.code,
        name: formState.name,
        parentId: formState.parentId,
      };

      const res = await addSubjectSave(todata);

      if (res.returnCode === '200') {
        message.success('新增科目成功');
        // 通知凭证页面刷新科目数据
        emitter.emit('account_voucher_subject_added', res.data);
        // 通知父组件刷新
        emits('refresh');
        ModalApi.close();
      } else {
        message.warning(res.returnMsg || '保存失败');
      }
    } catch (error) {
      console.error('新增科目失败:', error);
      message.error('新增科目失败');
    }
  };
</script>
<template>
  <Modal title="新增科目">
    <div>
      <a-form
        :model="formState"
        autocomplete="off"
        ref="formRef"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="上级科目"
          name="parentId"
          :rules="[{ required: true, message: '请选择上级科目' }]"
        >
          <a-select
            v-model:value="formState.parentId"
            placeholder="请选择上级科目"
            show-search
            :field-names="{
              label: 'label',
              value: 'value',
            }"
            :options="pureSubjectOptions"
          />
        </a-form-item>
        <a-form-item
          label="*科目编码"
          name="code"
          :rules="[{ required: true, message: '请输入科目编码' }]"
        >
          <a-input v-model:value="formState.code" placeholder="默认404" />
        </a-form-item>
        <a-form-item
          label="*科目名称"
          name="name"
          :rules="[{ required: true, message: '请输入科目名称' }]"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入科目名称"
          />
        </a-form-item>
      </a-form>
    </div>
  </Modal>
</template>
<style lang="scss" scoped>
  .btn {
    text-align: center;
  }

  .reminder {
    font-size: 10px;
    color: red;
  }
</style>
