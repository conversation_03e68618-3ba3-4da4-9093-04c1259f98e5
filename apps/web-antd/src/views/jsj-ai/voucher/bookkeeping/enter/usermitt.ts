import type { Emitter } from 'mitt';

import mitt from 'mitt';

type EventsJson = {
    id: string;
    text: string;
    type: 'abstract' | 'subject';
};
type Events = {
    account_voucher_newly_added: EventsJson; // 新增摘要新增后通知到表格
    account_voucher_subject_added: any; // 新增的科目
    account_voucher_auxiliary_added: any; // 新增的辅助核算
    quick_edit_set_voucher: any; // 快速编辑选择后设置凭证表格通知
};
const emitter: Emitter<Events> = mitt<Events>();
export default emitter;
