<script setup lang="ts">
  import type {
    UpdatePlatformUserInfoParams,
    UserPlatformInfo,
  } from '#/api/jsj-ai/types';

  import { h, onMounted, ref } from 'vue';

  import { useUserStore } from '@vben/stores';

  import { EditOutlined, UserOutlined } from '@ant-design/icons-vue';
  import {
    Button,
    Card,
    Form,
    Input,
    message,
    Modal,
    Spin,
    Tag,
  } from 'ant-design-vue';

  import {
    getUserCustomerNames,
    updatePlatformUserInfo,
  } from '#/api/jsj-ai/api-v2';

  // 用户信息
  const userStore = useUserStore();

  // 响应式数据
  const loading = ref(false);

  const updateLoading = ref(false);
  const userInfos = ref<UserPlatformInfo[]>([]);
  const accountModalVisible = ref(false);
  const accountFormRef = ref();

  // 账号配置表单数据
  const accountForm = ref({
    confirmPassword: '',
    data_source: '',
    password: '',
    username: '',
  });

  // 当前用户信息
  const currentUserInfo = ref({
    saas_id: '',
    tenant_id: '',
  });

  // 表单验证规则
  const accountRules: any = {
    confirmPassword: [
      { message: '请确认密码', required: true, trigger: 'blur' },
      {
        trigger: 'blur',
        validator: (_rule: any, value: string) => {
          if (value && value !== accountForm.value.password) {
            return Promise.reject(new Error('两次输入的密码不一致'));
          }
          return Promise.resolve();
        },
      },
    ],
    password: [
      { message: '请输入密码', required: true, trigger: 'blur' },
      { message: '密码长度不能少于6位', min: 6, trigger: 'blur' },
    ],
    username: [
      { message: '请输入用户名', required: true, trigger: 'blur' },
      { message: '用户名长度不能少于2位', min: 2, trigger: 'blur' },
    ],
  };

  // 获取数据源颜色
  const getDataSourceColor = (dataSource: string) => {
    const colorMap: Record<string, string> = {
      '17dz': 'green',
      jsj: 'blue',
    };
    return colorMap[dataSource] || 'default';
  };

  // 获取数据源名称
  const getDataSourceName = (dataSource: string) => {
    const nameMap: Record<string, string> = {
      '17dz': '17代账',
      jsj: '精算家',
    };
    return nameMap[dataSource] || dataSource;
  };

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      loading.value = true;
      const username = userStore.userInfo?.username;
      const tenantId = userStore.userInfo?.tenantId;

      if (!username || !tenantId) {
        message.error('用户信息不完整，请重新登录');
        return;
      }

      const response = await getUserCustomerNames({
        tenant_id: tenantId,
        username,
      });

      userInfos.value = response.user_infos || [];
      currentUserInfo.value = {
        saas_id: response.saas_id || '',
        tenant_id: tenantId,
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败');
    } finally {
      loading.value = false;
    }
  };

  // 打开账号配置弹窗
  const openAccountModal = (account: UserPlatformInfo) => {
    accountForm.value = {
      confirmPassword: '',
      data_source: account.data_source,
      password: '',
      username: account.username,
    };
    accountModalVisible.value = true;
  };

  // 处理账号配置更新
  const handleUpdateAccount = async () => {
    try {
      await accountFormRef.value.validate();

      updateLoading.value = true;

      const params: UpdatePlatformUserInfoParams = {
        data_source: accountForm.value.data_source,
        password: accountForm.value.password,
        saas_id: currentUserInfo.value.saas_id,
        tenant_id: currentUserInfo.value.tenant_id,
        username: accountForm.value.username,
      };

      await updatePlatformUserInfo(params);

      message.success('账号配置更新成功');
      accountModalVisible.value = false;
      resetAccountForm();
      // 重新查询页面数据
      await fetchUserInfo();
    } catch (error: any) {
      console.error('账号配置失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('账号配置更新失败，请稍后重试');
    } finally {
      updateLoading.value = false;
    }
  };

  // 取消账号配置
  const handleCancelAccountModal = () => {
    accountModalVisible.value = false;
    resetAccountForm();
  };

  // 重置账号配置表单
  const resetAccountForm = () => {
    accountForm.value = {
      confirmPassword: '',
      data_source: '',
      password: '',
      username: '',
    };
    accountFormRef.value?.resetFields();
  };

  // 组件挂载时获取用户信息
  onMounted(() => {
    fetchUserInfo();
  });
</script>

<template>
  <div class="account-config-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">账号配置</h2>
      <p class="page-description">管理您的三方平台账号信息</p>
    </div>

    <!-- 账号信息卡片 -->
    <Card class="account-info-card" title="账号信息">
      <div v-if="loading" class="loading-container">
        <Spin size="large" />
        <p class="loading-text">加载账号信息中...</p>
      </div>

      <div v-else-if="userInfos.length > 0" class="account-list">
        <div
          v-for="(account, index) in userInfos"
          :key="`${account.data_source}-${index}`"
          class="account-item"
        >
          <div class="account-info">
            <div class="account-header">
              <Tag
                :color="getDataSourceColor(account.data_source)"
                class="data-source-tag"
              >
                {{ getDataSourceName(account.data_source) }}
              </Tag>
              <span class="username">{{ account.username }}</span>
            </div>
          </div>

          <div class="account-actions">
            <Button
              type="primary"
              size="small"
              :icon="h(EditOutlined)"
              @click="openAccountModal(account)"
            >
              配置账号
            </Button>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <div class="empty-icon">
          <UserOutlined />
        </div>
        <p class="empty-text">暂无账号信息</p>
        <p class="empty-subtext">请联系管理员配置账号</p>
      </div>
    </Card>

    <!-- 账号配置弹窗 -->
    <Modal
      v-model:open="accountModalVisible"
      title="账号配置"
      :confirm-loading="updateLoading"
      @ok="handleUpdateAccount"
      @cancel="handleCancelAccountModal"
    >
      <Form
        ref="accountFormRef"
        :model="accountForm"
        :rules="accountRules"
        layout="vertical"
      >
        <Form.Item label="用户名" name="username">
          <Input
            v-model:value="accountForm.username"
            placeholder="请输入用户名"
          />
        </Form.Item>

        <Form.Item label="密码" name="password">
          <Input.Password
            v-model:value="accountForm.password"
            placeholder="请输入密码"
            autocomplete="new-password"
          />
        </Form.Item>

        <Form.Item label="确认密码" name="confirmPassword">
          <Input.Password
            v-model:value="accountForm.confirmPassword"
            placeholder="请再次输入密码"
            autocomplete="new-password"
          />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>

<style scoped>
  .account-config-container {
    min-height: 100vh;
    padding: 24px;
    background-color: #f5f5f5;
  }

  .page-header {
    margin-bottom: 24px;
  }

  .page-title {
    margin: 0 0 8px;
    font-size: 24px;
    font-weight: 600;
    color: #262626;
  }

  .page-description {
    margin: 0;
    font-size: 14px;
    color: #8c8c8c;
  }

  .account-info-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
  }

  .loading-text {
    margin-top: 16px;
    font-size: 14px;
    color: #8c8c8c;
  }

  .account-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .account-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .account-item:hover {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
  }

  .account-info {
    flex: 1;
  }

  .account-header {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .data-source-tag {
    font-weight: 500;
  }

  .username {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }

  .account-actions {
    display: flex;
    gap: 8px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
  }

  .empty-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #d9d9d9;
  }

  .empty-text {
    margin: 0 0 8px;
    font-size: 16px;
    color: #8c8c8c;
  }

  .empty-subtext {
    margin: 0;
    font-size: 14px;
    color: #bfbfbf;
  }
</style>
