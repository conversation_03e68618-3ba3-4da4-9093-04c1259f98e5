/**
 * 配置文件加载器
 * 用于从public/config.json读取运行时配置
 */

export interface AppConfig {
  websocket: {
    aiChatUrl: string;
    voucherUrl: string;
  };
  api: {
    baseUrl: string;
    fileServerUrl: string;
  };
  environment: string;
}

let cachedConfig: AppConfig | null = null;

/**
 * 获取应用配置
 * @returns Promise<AppConfig>
 */
export async function getAppConfig(): Promise<AppConfig> {
  // 如果已经缓存了配置，直接返回
  if (cachedConfig) {
    return cachedConfig;
  }

  try {
    // 从public目录读取config.json
    const response = await fetch('/config.json');
    
    if (!response.ok) {
      throw new Error(`Failed to load config.json: ${response.status} ${response.statusText}`);
    }
    
    const config: AppConfig = await response.json();
    
    // 验证配置结构
    if (!config.websocket) {
      throw new Error('Invalid config.json structure');
    }
    
    // 缓存配置
    cachedConfig = config;
    
    console.log('配置文件加载成功:', config);
    return config;
  } catch (error) {
    console.error('加载配置文件失败:', error);
    
    // 返回默认配置作为后备
    const defaultConfig: AppConfig = {
      websocket: {
        aiChatUrl: 'ws://**************:30065',
        voucherUrl: 'ws://**************:8080'
      },
      api: {
        baseUrl: '/prod-api',
        fileServerUrl: '/prod-api/autojob/files'
      },
      environment: 'production'
    };
    
    console.warn('使用默认配置:', defaultConfig);
    cachedConfig = defaultConfig;
    return defaultConfig;
  }
}

/**
 * 获取WebSocket配置
 * @returns Promise<AppConfig['websocket']>
 */
export async function getWebSocketConfig() {
  const config = await getAppConfig();
  return config.websocket;
}

/**
 * 获取API配置
 * @returns Promise<AppConfig['api']>
 */
export async function getApiConfig() {
  const config = await getAppConfig();
  return config.api;
}

/**
 * 清除配置缓存（用于测试或重新加载配置）
 */
export function clearConfigCache() {
  cachedConfig = null;
}

/**
 * 检查是否为开发环境
 * @returns boolean
 */
export function isDevelopmentMode(): boolean {
  return import.meta.env.DEV;
}

/**
 * 获取AI聊天WebSocket地址
 * 优先级：环境变量 > config.json > 默认值
 * @returns Promise<string>
 */
export async function getAiChatWebSocketUrl(): Promise<string> {
  // 优先使用环境变量配置
  const envWsUrl = import.meta.env.VITE_AI_CHAT_WS_URL;
  if (envWsUrl) {
    return envWsUrl;
  }

  // 从配置文件获取
  try {
    const wsConfig = await getWebSocketConfig();
    return wsConfig.aiChatUrl;
  } catch (error) {
    console.error('获取WebSocket配置失败，使用默认值:', error);
    
    // 判断是否为本地运行（localhost）
    const isLocalhost = window.location.hostname === 'localhost';
    if (isLocalhost) {
      // 本地运行使用固定IP
      return 'ws://**************:30065';
    } else {
      // 其他情况使用当前域名
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.hostname;
      return `${protocol}//${host}:30065`;
    }
  }
}

