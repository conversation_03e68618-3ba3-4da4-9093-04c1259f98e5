# WebSocket地址配置示例

## 新的逻辑说明

现在WebSocket地址配置遵循以下逻辑：

1. **优先级1**：环境变量 `VITE_AI_CHAT_WS_URL`
2. **优先级2**：根据当前访问域名判断
   - 本地运行（localhost/内网IP）：使用固定IP `ws://**************:30065`
   - 其他情况：使用当前域名 + 端口30065

## 使用示例

### 本地运行情况

当访问地址为以下任一情况时，使用固定IP：

```javascript
// 访问地址: http://localhost:5666
// WebSocket: ws://**************:30065

// 访问地址: http://127.0.0.1:5666
// WebSocket: ws://**************:30065

// 访问地址: http://*************:5666
// WebSocket: ws://**************:30065

// 访问地址: http://********:5666
// WebSocket: ws://**************:30065

// 访问地址: http://**********:5666
// WebSocket: ws://**************:30065
```

### 外网部署情况

当访问地址为外网域名时，使用当前域名：

```javascript
// 访问地址: http://example.com
// WebSocket: ws://example.com:30065

// 访问地址: https://secure.example.com
// WebSocket: wss://secure.example.com:30065

// 访问地址: http://production.server.com
// WebSocket: ws://production.server.com:30065
```

### 环境变量优先

如果设置了环境变量，则优先使用：

```bash
# .env.development 或 .env.production
VITE_AI_CHAT_WS_URL=ws://custom.server:8080
```

```javascript
// 无论访问地址是什么，都会使用：
// WebSocket: ws://custom.server:8080
```

## 代码使用

```typescript
import { getAiChatWebSocketUrl } from '#/utils/websocket-url';

// 获取WebSocket地址
const wsUrl = getAiChatWebSocketUrl();
console.log('WebSocket地址:', wsUrl);

// 在组件中使用
const WS_URL = getAiChatWebSocketUrl();
const { wsOpen, wsSend } = useWebSocketConnection(WS_URL);
```

## 判断函数

```typescript
import { isLocalhost, isLocalDevelopment, isProduction } from '#/utils/websocket-url';

// 判断是否为本地运行环境（localhost或内网IP）
if (isLocalhost()) {
  console.log('当前在本地运行');
}

// 判断是否为开发环境
if (isLocalDevelopment()) {
  console.log('当前为开发环境');
}

// 判断是否为生产环境
if (isProduction()) {
  console.log('当前为生产环境');
}
```
