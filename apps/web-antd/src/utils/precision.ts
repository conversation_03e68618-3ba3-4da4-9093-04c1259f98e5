/**
 * 金额精度计算工具函数
 * 使用 number-precision 库解决 JavaScript 浮点数精度问题
 */

import NP from 'number-precision';

/**
 * 精确加法
 * @param num1 加数1
 * @param num2 加数2
 * @returns 精确的加法结果
 */
export function precisionAdd(num1: number, num2: number): number {
  return NP.plus(num1, num2);
}

/**
 * 精确减法
 * @param num1 被减数
 * @param num2 减数
 * @returns 精确的减法结果
 */
export function precisionSubtract(num1: number, num2: number): number {
  return NP.minus(num1, num2);
}

/**
 * 精确乘法
 * @param num1 乘数1
 * @param num2 乘数2
 * @returns 精确的乘法结果
 */
export function precisionMultiply(num1: number, num2: number): number {
  return NP.times(num1, num2);
}

/**
 * 精确除法
 * @param num1 被除数
 * @param num2 除数
 * @returns 精确的除法结果
 */
export function precisionDivide(num1: number, num2: number): number {
  return NP.divide(num1, num2);
}

/**
 * 精确四舍五入
 * @param num 要四舍五入的数字
 * @param digits 保留的小数位数，默认2位
 * @returns 四舍五入后的结果
 */
export function precisionRound(num: number, digits: number = 2): number {
  return NP.round(num, digits);
}

/**
 * 金额数组求和（精确计算）
 * @param amounts 金额数组
 * @returns 精确的求和结果
 */
export function precisionSum(amounts: number[]): number {
  return amounts.reduce((sum, amount) => precisionAdd(sum, amount || 0), 0);
}

/**
 * 格式化金额显示
 * @param amount 金额
 * @param digits 小数位数，默认2位
 * @param locale 地区设置，默认中文
 * @returns 格式化后的金额字符串
 */
export function formatAmount(
  amount: number,
  digits: number = 2,
  locale: string = 'zh-CN'
): string {
  if (amount === undefined || amount === null) {
    return '0.00';
  }
  
  // 先进行精确四舍五入
  const roundedAmount = precisionRound(amount, digits);
  
  // 然后格式化显示
  return roundedAmount.toLocaleString(locale, {
    minimumFractionDigits: digits,
    maximumFractionDigits: digits,
  });
}

/**
 * 检查两个金额是否相等（考虑精度问题）
 * @param amount1 金额1
 * @param amount2 金额2
 * @param tolerance 容差，默认0.01（1分钱）
 * @returns 是否相等
 */
export function isAmountEqual(
  amount1: number,
  amount2: number,
  tolerance: number = 0.01
): boolean {
  return Math.abs(precisionSubtract(amount1, amount2)) < tolerance;
}

/**
 * 银行回单统计计算
 * @param data 银行回单数据数组
 * @returns 统计结果
 */
export function calculateBankReceiptSummary(data: Array<{ type: string; amount: number }>) {
  let incomeCount = 0;
  let incomeAmount = 0;
  let expenseCount = 0;
  let expenseAmount = 0;

  data.forEach((item) => {
    if (item.type === '收入') {
      incomeCount++;
      incomeAmount = precisionAdd(incomeAmount, item.amount || 0);
    } else if (item.type === '支出') {
      expenseCount++;
      expenseAmount = precisionAdd(expenseAmount, item.amount || 0);
    }
  });

  return {
    incomeCount,
    incomeAmount: precisionRound(incomeAmount, 2),
    expenseCount,
    expenseAmount: precisionRound(expenseAmount, 2),
  };
}

/**
 * 发票统计计算
 * @param data 发票数据数组
 * @returns 统计结果
 */
export function calculateInvoiceSummary(data: Array<{
  total_amount: number;
  total_tax: number;
  total: number;
  is_detail_row?: boolean;
}>) {
  let count = 0;
  let totalAmount = 0; // 不含税金额合计
  let taxAmount = 0;   // 税额合计
  let totalWithTax = 0; // 价税合计

  data.forEach((item) => {
    // 如果是货物维度展示，只统计非明细行（即发票级别的数据）
    if (item.is_detail_row === true) {
      return;
    }

    count++;
    totalAmount = precisionAdd(totalAmount, item.total_amount || 0);
    taxAmount = precisionAdd(taxAmount, item.total_tax || 0);
    totalWithTax = precisionAdd(totalWithTax, item.total || 0);
  });

  return {
    count,
    totalAmount: precisionRound(totalAmount, 2),
    taxAmount: precisionRound(taxAmount, 2),
    totalWithTax: precisionRound(totalWithTax, 2),
  };
}
