/**
 * WebSocket 消息过滤和验证工具
 */

export interface MessageValidationResult {
  isValid: boolean;
  error?: string;
  data?: any;
  type?: 'json' | 'text' | 'binary' | 'empty' | 'undefined';
}

/**
 * 验证和解析 WebSocket 消息
 * @param rawData 原始消息数据
 * @returns 验证结果
 */
export function validateWebSocketMessage(rawData: any): MessageValidationResult {
  // 检查是否为 undefined 或 null
  if (rawData === undefined) {
    return {
      isValid: false,
      error: '收到 undefined 消息',
      type: 'undefined',
    };
  }

  if (rawData === null) {
    return {
      isValid: false,
      error: '收到 null 消息',
      type: 'empty',
    };
  }

  // 检查是否为空字符串
  if (rawData === '') {
    return {
      isValid: false,
      error: '收到空字符串消息',
      type: 'empty',
    };
  }

  // 检查是否为字符串 "undefined"
  if (rawData === 'undefined') {
    return {
      isValid: false,
      error: '收到字符串 "undefined"',
      type: 'undefined',
    };
  }

  // 检查是否为字符串类型
  if (typeof rawData === 'string') {
    // 尝试解析 JSON
    try {
      const parsed = JSON.parse(rawData);
      return {
        isValid: true,
        data: parsed,
        type: 'json',
      };
    } catch (error) {
      // 如果不是 JSON，检查是否是有意义的文本
      const trimmed = rawData.trim();
      if (trimmed.length === 0) {
        return {
          isValid: false,
          error: '收到只包含空白字符的消息',
          type: 'empty',
        };
      }

      // 返回纯文本消息
      return {
        isValid: true,
        data: trimmed,
        type: 'text',
      };
    }
  }

  // 检查是否为二进制数据
  if (rawData instanceof ArrayBuffer || rawData instanceof Blob) {
    return {
      isValid: true,
      data: rawData,
      type: 'binary',
    };
  }

  // 其他类型的数据
  return {
    isValid: true,
    data: rawData,
    type: 'text',
  };
}

/**
 * 过滤心跳消息和系统消息
 * @param data 解析后的消息数据
 * @returns 是否应该处理这个消息
 */
export function shouldProcessMessage(data: any): boolean {
  // 如果是对象，检查是否是心跳消息
  if (typeof data === 'object' && data !== null) {
    // 常见的心跳消息类型
    const heartbeatTypes = ['ping', 'pong', 'heartbeat', 'keepalive'];
    if (data.type && heartbeatTypes.includes(data.type.toLowerCase())) {
      return false;
    }

    // 检查是否是空对象
    if (Object.keys(data).length === 0) {
      return false;
    }
  }

  // 如果是字符串，检查是否是心跳消息
  if (typeof data === 'string') {
    const lowerData = data.toLowerCase();
    const heartbeatStrings = ['ping', 'pong', 'heartbeat', 'keepalive'];
    if (heartbeatStrings.includes(lowerData)) {
      return false;
    }
  }

  return true;
}

/**
 * 安全的 WebSocket 消息处理器
 * @param rawData 原始消息数据
 * @param handler 消息处理函数
 * @param options 选项
 */
export function safeWebSocketMessageHandler(
  rawData: any,
  handler: (data: any) => void,
  options: {
    logInvalidMessages?: boolean;
    logHeartbeat?: boolean;
    onError?: (error: string, rawData: any) => void;
  } = {},
) {
  const { logInvalidMessages = true, logHeartbeat = false, onError } = options;

  // 验证消息
  const validation = validateWebSocketMessage(rawData);

  if (!validation.isValid) {
    if (logInvalidMessages) {
      console.warn(`WebSocket 消息验证失败: ${validation.error}`, rawData);
    }
    if (onError) {
      onError(validation.error || '未知错误', rawData);
    }
    return;
  }

  // 检查是否应该处理这个消息
  if (!shouldProcessMessage(validation.data)) {
    if (logHeartbeat) {
      console.debug('跳过心跳消息:', validation.data);
    }
    return;
  }

  // 处理有效消息
  try {
    handler(validation.data);
  } catch (error) {
    console.error('处理 WebSocket 消息时出错:', error);
    if (onError) {
      onError(`处理消息时出错: ${error}`, rawData);
    }
  }
}

/**
 * 创建 WebSocket 消息统计器
 */
export function createWebSocketMessageStats() {
  const stats = {
    total: 0,
    valid: 0,
    invalid: 0,
    heartbeat: 0,
    json: 0,
    text: 0,
    binary: 0,
    errors: [] as Array<{ time: Date; error: string; data: any }>,
  };

  const addMessage = (rawData: any) => {
    stats.total++;
    
    const validation = validateWebSocketMessage(rawData);
    
    if (!validation.isValid) {
      stats.invalid++;
      stats.errors.push({
        time: new Date(),
        error: validation.error || '未知错误',
        data: rawData,
      });
      return;
    }

    stats.valid++;

    // 统计消息类型
    switch (validation.type) {
      case 'json':
        stats.json++;
        break;
      case 'text':
        stats.text++;
        break;
      case 'binary':
        stats.binary++;
        break;
    }

    // 检查是否是心跳消息
    if (!shouldProcessMessage(validation.data)) {
      stats.heartbeat++;
    }

    // 限制错误日志数量
    if (stats.errors.length > 100) {
      stats.errors = stats.errors.slice(-50);
    }
  };

  const getStats = () => ({ ...stats });
  
  const reset = () => {
    stats.total = 0;
    stats.valid = 0;
    stats.invalid = 0;
    stats.heartbeat = 0;
    stats.json = 0;
    stats.text = 0;
    stats.binary = 0;
    stats.errors = [];
  };

  return {
    addMessage,
    getStats,
    reset,
  };
}
