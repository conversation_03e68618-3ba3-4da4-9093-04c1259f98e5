import { useAppConfig } from '@vben/hooks';

/**
 * 文件URL构建工具类
 * 统一管理文件服务器URL的构建逻辑
 */
export class FileUrlBuilder {
  private static fileServerURL: string;

  /**
   * 初始化文件服务器URL
   */
  private static initFileServerURL(): void {
    if (!this.fileServerURL) {
      const appConfig = useAppConfig(import.meta.env, import.meta.env.PROD);
      this.fileServerURL = appConfig.fileServerURL;
    }
  }

  /**
   * 构建完整的文件URL
   * @param filename 文件名
   * @param companyName 公司名称
   * @param isBankReceipt 是否为银行回单（需要添加月份路径）
   * @param month 月份（YYYYMM格式）
   * @returns 完整的文件URL
   */
  static buildFileUrl(
    filename: string,
    companyName: string,
    isBankReceipt: boolean = false,
    month?: string,
  ): string {
    this.initFileServerURL();

    if (!filename || !companyName) {
      console.warn('文件名或公司名称为空，无法构建文件URL');
      return '#';
    }

    // 对公司名称进行URL编码
    const encodedCompanyName = encodeURIComponent(companyName);

    // 如果提供了月份参数，在公司名称后面加上月份路径
    // 这适用于银行回单和发票等需要按月份组织的文件
    if (month) {
      return `${this.fileServerURL}/${encodedCompanyName}/${month}/${filename}`;
    }

    return `${this.fileServerURL}/${encodedCompanyName}/${filename}`;
  }

  /**
   * 构建PDF凭证文件URL
   * @param filename PDF文件名
   * @param companyName 公司名称
   * @param month 月份（YYYYMM格式）
   * @returns PDF文件的完整URL
   */
  static buildVoucherPdfUrl(
    filename: string,
    companyName: string,
    month: string,
  ): string {
    this.initFileServerURL();

    if (!filename || !companyName || !month) {
      console.warn('PDF文件名、公司名称或月份为空，无法构建PDF URL');
      return '#';
    }

    const encodedCompanyName = encodeURIComponent(companyName);
    const encodedFileName = encodeURIComponent(filename);

    return `${this.fileServerURL}/${encodedCompanyName}/${month}/voucher/${encodedFileName}`;
  }

  /**
   * 构建发票文件URL（根据digital_invoice_number）
   * @param digitalInvoiceNumber 数字发票号码
   * @param companyName 公司名称
   * @param month 月份（YYYYMM格式）
   * @returns 发票PDF文件的完整URL
   */
  static buildInvoicePdfUrl(
    digitalInvoiceNumber: string,
    companyName: string,
    month: string,
  ): string {
    this.initFileServerURL();

    if (!digitalInvoiceNumber || !companyName || !month) {
      console.warn('发票号码、公司名称或月份为空，无法构建发票PDF URL');
      return '#';
    }

    const encodedCompanyName = encodeURIComponent(companyName);

    return `${this.fileServerURL}/${encodedCompanyName}/${month}/${digitalInvoiceNumber}.pdf`;
  }

  /**
   * 获取文件服务器基础URL
   * @returns 文件服务器基础URL
   */
  static getFileServerURL(): string {
    this.initFileServerURL();
    return this.fileServerURL;
  }
}

/**
 * 构建文件URL的便捷函数
 * @param filename 文件名
 * @param companyName 公司名称
 * @param isBankReceipt 是否为银行回单
 * @param month 月份
 * @returns 完整的文件URL
 */
export function buildFileUrl(
  filename: string,
  companyName: string,
  isBankReceipt: boolean = false,
  month?: string,
): string {
  return FileUrlBuilder.buildFileUrl(filename, companyName, isBankReceipt, month);
}

/**
 * 构建PDF凭证文件URL的便捷函数
 * @param filename PDF文件名
 * @param companyName 公司名称
 * @param month 月份
 * @returns PDF文件的完整URL
 */
export function buildVoucherPdfUrl(
  filename: string,
  companyName: string,
  month: string,
): string {
  return FileUrlBuilder.buildVoucherPdfUrl(filename, companyName, month);
}

/**
 * 构建发票PDF文件URL的便捷函数
 * @param digitalInvoiceNumber 数字发票号码
 * @param companyName 公司名称
 * @param month 月份
 * @returns 发票PDF文件的完整URL
 */
export function buildInvoicePdfUrl(
  digitalInvoiceNumber: string,
  companyName: string,
  month: string,
): string {
  return FileUrlBuilder.buildInvoicePdfUrl(digitalInvoiceNumber, companyName, month);
}
