/**
 * WebSocket地址动态配置工具
 * 根据运行环境自动选择合适的WebSocket地址
 *
 * @deprecated 请使用 config-loader.ts 中的 getAiChatWebSocketUrl() 函数
 * 该函数支持从 config.json 文件读取配置
 */

import { getAiChatWebSocketUrl as getAiChatWebSocketUrlFromConfig } from './config-loader';

/**
 * 获取AI聊天WebSocket地址
 * @returns WebSocket地址
 * @deprecated 请使用 config-loader.ts 中的 getAiChatWebSocketUrl() 函数
 */
export function getAiChatWebSocketUrl(): string {
  // 优先使用环境变量配置
  const envWsUrl = import.meta.env.VITE_AI_CHAT_WS_URL;
  if (envWsUrl) {
    return envWsUrl;
  }

  // 判断是否为本地运行（localhost）
  const isLocalhost = window.location.hostname === 'localhost'

  if (isLocalhost) {
    // 本地运行使用固定IP
    return 'ws://**************:30065';
  } else {
    // 其他情况使用当前域名
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    return `${protocol}//${host}:30065`;
  }
}

/**
 * 获取AI聊天WebSocket地址（异步版本，支持config.json）
 * @returns Promise<string> WebSocket地址
 */
export async function getAiChatWebSocketUrlAsync(): Promise<string> {
  return await getAiChatWebSocketUrlFromConfig();
}

/**
 * 获取通用WebSocket地址
 * @param port 端口号
 * @param envKey 环境变量键名
 * @param fallbackHost 本地运行时的回退主机地址
 * @returns WebSocket地址
 */
export function getWebSocketUrl(
  port: number,
  envKey?: string,
  fallbackHost: string = '**************'
): string {
  // 优先使用环境变量配置
  if (envKey) {
    const envWsUrl = import.meta.env[envKey];
    if (envWsUrl) {
      return envWsUrl;
    }
  }

  // 判断是否为本地运行（localhost）
  const isLocalhost = window.location.hostname === 'localhost' 

  if (isLocalhost) {
    // 本地运行使用指定的回退主机
    return `ws://${fallbackHost}:${port}`;
  } else {
    // 其他情况使用当前域名
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    return `${protocol}//${host}:${port}`;
  }
}

/**
 * 判断是否为本地运行环境（localhost或内网IP）
 * @returns 是否为本地运行环境
 */
export function isLocalhost(): boolean {
  return window.location.hostname === 'localhost' ||
         window.location.hostname === '127.0.0.1' ||
         window.location.hostname.startsWith('192.168.') ||
         window.location.hostname.startsWith('10.') ||
         window.location.hostname.startsWith('172.');
}

/**
 * 判断是否为本地开发环境
 * @returns 是否为本地开发环境
 */
export function isLocalDevelopment(): boolean {
  return import.meta.env.DEV;
}

/**
 * 判断是否为生产环境
 * @returns 是否为生产环境
 */
export function isProduction(): boolean {
  return import.meta.env.PROD;
}
