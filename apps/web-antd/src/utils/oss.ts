import OSS from 'ali-oss';

interface OSSConfig {
  region: string;
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  secure: boolean;
}

export class OSSUploader {
  private baseDir: string;
  private client: OSS;

  constructor(config: OSSConfig, baseDir: string = 'chat-uploads/') {
    this.client = new OSS(config);
    this.baseDir = baseDir;
  }

  async uploadFile(file: File): Promise<string> {
    try {
      // 生成唯一的文件名
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).slice(2, 8);
      const ext = file.name.split('.').pop();
      const ossPath = `${this.baseDir}${timestamp}-${randomStr}.${ext}`;

      // 上传文件到 OSS
      const result = await this.client.put(ossPath, file);

      // 返回文件的 URL
      return result.url;
    } catch (error) {
      console.error('OSS upload failed:', error);
      throw new Error('文件上传失败');
    }
  }

  async uploadMultipleFiles(files: File[]): Promise<string[]> {
    const uploadPromises = files.map((file) => this.uploadFile(file));
    return Promise.all(uploadPromises);
  }
}

// 创建默认的 OSS 实例
// 注意：实际使用时应该从环境变量或配置文件中获取这些值
const defaultConfig: OSSConfig = {
  region: import.meta.env.VITE_OSS_REGION || 'oss-cn-hangzhou',
  accessKeyId:
    import.meta.env.VITE_OSS_ACCESS_KEY_ID || 'LTAI5tR75ghAKB6oQr19NYjk',
  accessKeySecret:
    import.meta.env.VITE_OSS_ACCESS_KEY_SECRET ||
    '******************************',
  bucket: import.meta.env.VITE_OSS_BUCKET || 'jsj-test-oss',
  // region: import.meta.env.VITE_OSS_REGION || 'cn-hangzhou',
  // accessKeyId:
  //   import.meta.env.VITE_OSS_ACCESS_KEY_ID || 'LTAI5tR75ghAKB6oQr19NYjk',
  // accessKeySecret:
  //   import.meta.env.VITE_OSS_ACCESS_KEY_SECRET ||
  //   '******************************',
  // bucket: import.meta.env.VITE_OSS_BUCKET || 'jsj-test-oss',
  secure: true,
};

export const ossUploader = new OSSUploader(defaultConfig);
