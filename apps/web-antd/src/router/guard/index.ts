import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router';
import { useMenuModeStore } from '#/store/modules/menu-mode';
import { customerListMenu, workspaceMenus, localRoutes } from '../routes/local';

/**
 * 菜单模式守卫
 */
export function createMenuModeGuard(router: Router) {
  router.beforeEach(async (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    const menuModeStore = useMenuModeStore();
    const isCustomerListMode = menuModeStore.isCustomerListMode();

    // 检查是否是公共路由（比如登录、404等）
    const isPublicRoute = localRoutes.some(route => 
      to.path.startsWith(route.path)
    );

    // 如果是公共路由，直接放行
    if (isPublicRoute) {
      next();
      return;
    }

    // 检查目标路由是否在当前可访问的菜单中
    const isCustomerListRoute = customerListMenu.some(menu => 
      to.path.startsWith(menu.path)
    );
    const isWorkspaceRoute = workspaceMenus.some(menu => 
      to.path.startsWith(menu.path) || 
      (menu.children?.some(child => to.path.startsWith(child.path)))
    );

    // 如果路由不在任何已知菜单中，重定向到相应的首页
    if (!isCustomerListRoute && !isWorkspaceRoute) {
      console.log('未知路由，重定向到对应模式的首页');
      next(isCustomerListMode ? '/company-status' : '/voucher/original');
      return;
    }

    // 在客户列表模式下访问工作台页面，自动切换到工作台模式
    if (isCustomerListMode && isWorkspaceRoute) {
      try {
        await menuModeStore.setMenuMode('workspace');
        next({ path: to.path, replace: true });
      } catch (error) {
        console.error('切换到工作台模式失败:', error);
        next('/company-status');
      }
      return;
    }

    // 在工作台模式下访问客户列表页面，自动切换到客户列表模式
    if (!isCustomerListMode && isCustomerListRoute) {
      try {
        await menuModeStore.setMenuMode('customer-list');
        next({ path: to.path, replace: true });
      } catch (error) {
        console.error('切换到客户列表模式失败:', error);
        next('/voucher/original');
      }
      return;
    }

    next();
  });
}

/**
 * 设置路由守卫
 */
export function setupRouterGuard(router: Router) {
  createMenuModeGuard(router);
} 
