import type {
  ComponentRecordType,
  GenerateMenuAndRoutesOptions,
  RouteRecordStringComponent,
} from '@vben/types';

import type { Menu } from '#/api';

import { generateAccessible } from '@vben/access';
import { preferences } from '@vben/preferences';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { getAllMenusApi } from '#/api';
import { BasicLayout, IFrameView } from '#/layouts';
import { $t } from '#/locales';

import { customerListMenu, workspaceMenus, localRoutes } from './routes/local';
import { useMenuModeStore } from '#/store/modules/menu-mode';
import { useAccessStore } from '@vben/stores';
import { accessRoutes } from './routes';

const forbiddenComponent = () => import('#/views/_core/fallback/forbidden.vue');
const NotFoundComponent = () => import('#/views/_core/fallback/not-found.vue');

/**
 * 后台路由转vben路由
 * @param menuList 后台菜单
 * @param parentPath 上级目录
 * @returns vben路由
 */
function backMenuToVbenMenu(
  menuList: Menu[],
  parentPath = '',
): RouteRecordStringComponent[] {
  const resultList: RouteRecordStringComponent[] = [];
  menuList.forEach((menu) => {
    // 根目录为菜单形式
    // 固定有一个children  children为当前菜单
    if (menu.path === '/' && menu.children && menu.children.length === 1) {
      if (!menu.children || !menu.children[0]) {
        return;
      }

      // 需要处理根目录为内嵌的情况 不会带InnerLink
      if (/^https?:\/\//.test(menu.children[0].path)) {
        menu.children[0].component = 'InnerLink';
        menu.children[0].path = menu.children[0].path
          .replaceAll(/^https?:\/\//g, '')
          .replaceAll('/#/', '')
          .replaceAll('#', '')
          .replaceAll(/[?&]/g, '');
      }

      // 取子路径作为父级路径
      const path = menu.children[0].path;
      // 取子菜单的meta作为当前菜单的meta
      menu.meta = menu.children[0].meta;
      // 由于在一级路由 父级路径需要加上/
      menu.path = `/${path}`;
      menu.component = 'RootMenu';
      // 将子路径设置为''
      menu.children[0].path = '';
    }

    // 外链: http开头 & 组件为Layout || ParentView
    // 正则判断是否为http://或者https://开头
    if (
      /^https?:\/\//.test(menu.path) &&
      (menu.component === 'Layout' || menu.component === 'ParentView')
    ) {
      menu.component = 'Link';
    }

    // 内嵌iframe 组件为InnerLink
    if (menu.meta?.link && menu.component === 'InnerLink') {
      menu.component = 'IFrameView';
    }

    /**
     * 拼接path
     * menu.path为''(根目录路由) 则不拼接
     */
    if (parentPath && menu.path) {
      menu.path = `${parentPath}/${menu.path}`;
    }

    // 创建vben路由对象
    const vbenRoute: RouteRecordStringComponent = {
      component: menu.component,
      meta: {
        // 当前路由不在菜单显示 但是可以通过链接访问
        // 不可访问的路由由后端控制隐藏(不返回对应路由)
        hideInMenu: menu.hidden,
        icon: menu.meta?.icon,
        keepAlive: !menu.meta?.noCache,
        title: menu.meta?.title,
      },
      name: menu.name,
      path: menu.path,
    };

    // 添加路由参数信息
    if (menu.query) {
      try {
        const query = JSON.parse(menu.query);
        vbenRoute.meta && (vbenRoute.meta.query = query);
      } catch {
        console.error('错误的路由参数类型, 必须为[json]格式');
      }
    }

    /**
     * 处理不同组件
     */
    switch (menu.component) {
      /**
       * iframe内嵌
       */
      case 'IFrameView': {
        vbenRoute.component = 'IFrameView';
        if (vbenRoute.meta) {
          vbenRoute.meta.iframeSrc = menu.meta.link;
        }
        /**
         * 需要判断特殊情况  比如vue的hash是带#的
         * 比如链接 aaa.com/#/bbb  path会转换为 aaa/com/#/bbb
         * 比如链接 aaa.com/?bbb=xxx
         * 需要去除#  否则无法被添加到路由
         */
        vbenRoute.path = vbenRoute.path
          // 替换https:// 或者 http://
          .replaceAll(/^https?:\/\//g, '')
          .replaceAll('/#/', '')
          .replaceAll('#', '')
          .replaceAll(/[?&]/g, '');
        break;
      }
      case 'Layout': {
        vbenRoute.component = 'BasicLayout';
        break;
      }
      /**
       * 外链 新窗口打开
       */
      case 'Link': {
        if (vbenRoute.meta) {
          vbenRoute.meta.link = menu.meta.link;
        }
        vbenRoute.component = 'BasicLayout';
        break;
      }
      /**
       * 三级以上菜单 父级component为ParentView
       * 不能为layout 会套两层BasicLayout
       */
      case 'ParentView': {
        vbenRoute.component = '';
        break;
      }
      /**
       * 根目录菜单
       */
      case 'RootMenu': {
        if (vbenRoute.meta) {
          vbenRoute.meta.hideChildrenInMenu = true;
        }
        vbenRoute.component = 'BasicLayout';
        break;
      }
      /**
       * 其他自定义组件 如system/user/index 拼接/
       */
      default: {
        vbenRoute.component = `/${menu.component}`;
        break;
      }
    }

    // children处理
    if (menu.children && menu.children.length > 0) {
      vbenRoute.children = backMenuToVbenMenu(menu.children, menu.path);
    }
    // 添加
    resultList.push(vbenRoute);
  });
  return resultList;
}

async function generateAccess(options: GenerateMenuAndRoutesOptions) {
  const pageMap: ComponentRecordType = import.meta.glob('../views/**/*.vue');

  const layoutMap: ComponentRecordType = {
    BasicLayout,
    IFrameView,
    NotFoundComponent,
  };

  return await generateAccessible(preferences.app.accessMode, {
    ...options,
    fetchMenuListAsync: async () => {
      // 清除以前的message
      message.destroy();
      message.loading({
        content: '加载菜单中...',
        duration: 0.5,
      });

      // 使用本地路由配置，不再从后端API获取
      // 如果需要从后端获取，可以取消下面的注释
      // const backMenuList = await getAllMenusApi();
      // const vbenMenuList = backMenuToVbenMenu(backMenuList);
      // const menuList = [...cloneDeep(localMenuList), ...vbenMenuList];

      // 根据菜单模式动态返回菜单配置
      const menuModeStore = useMenuModeStore();
      const isCustomerListMode = menuModeStore.isCustomerListMode();



      // 递归检查是否属于工作台菜单
      const isWorkspaceRoute = (route: any): boolean => {
        // 检查顶级菜单
        if (workspaceMenus.some(w => w.name === route.name)) {
          return true;
        }
        // 检查是否是工作台菜单的子菜单
        return workspaceMenus.some(w => {
          if (w.children) {
            return w.children.some((child: any) => child.name === route.name);
          }
          return false;
        });
      };

      // 递归检查是否属于客户列表菜单
      const isCustomerRoute = (route: any): boolean => {
        // 检查顶级菜单
        if (customerListMenu.some(c => c.name === route.name)) {
          return true;
        }
        // 检查是否是客户列表菜单的子菜单
        return customerListMenu.some(c => {
          if (c.children) {
            return c.children.some((child: any) => child.name === route.name);
          }
          return false;
        });
      };

      // 递归处理菜单项，确保正确处理子菜单的hideInMenu属性
      const processMenuItem = (route: any): any => {
        const isWorkspaceMenu = isWorkspaceRoute(route);
        const isCustomerMenu = isCustomerRoute(route);

        // 在客户列表模式下隐藏工作台菜单，在工作台模式下隐藏客户列表菜单
        const shouldHide = (isCustomerListMode && isWorkspaceMenu) ||
                         (!isCustomerListMode && isCustomerMenu);

        const processedRoute = {
          ...route,
          meta: {
            ...route.meta,
            hideInMenu: shouldHide || route.meta?.hideInMenu,
          },
        };



        // 递归处理子菜单
        if (processedRoute.children && processedRoute.children.length > 0) {
          processedRoute.children = processedRoute.children.map((child: any) => processMenuItem(child));
        }

        return processedRoute;
      };

      // 始终包含所有路由，但根据模式设置hideInMenu
      const allMenus = cloneDeep([...customerListMenu, ...workspaceMenus, ...localRoutes]);
      const menuList = allMenus.map(route => processMenuItem(route));


      return menuList;
    },
    // 可以指定没有权限跳转403页面
    forbiddenComponent,
    // 如果 route.meta.menuVisibleWithForbidden = true
    layoutMap,
    pageMap,
  });
}

/**
 * 重新生成菜单和路由（用于菜单模式切换）
 */
export async function regenerateMenus(router?: any) {
  const accessStore = useAccessStore();
  const menuModeStore = useMenuModeStore();
  const isCustomerListMode = menuModeStore.isCustomerListMode();

  // 递归检查是否属于工作台菜单
  const isWorkspaceRoute = (route: any): boolean => {
    // 检查顶级菜单
    if (workspaceMenus.some(w => w.name === route.name)) {
      return true;
    }
    // 检查是否是工作台菜单的子菜单
    return workspaceMenus.some(w => {
      if (w.children) {
        return w.children.some((child: any) => child.name === route.name);
      }
      return false;
    });
  };

  // 递归检查是否属于客户列表菜单
  const isCustomerRoute = (route: any): boolean => {
    // 检查顶级菜单
    if (customerListMenu.some(c => c.name === route.name)) {
      return true;
    }
    // 检查是否是客户列表菜单的子菜单
    return customerListMenu.some(c => {
      if (c.children) {
        return c.children.some((child: any) => child.name === route.name);
      }
      return false;
    });
  };

  // 递归处理菜单项，确保正确处理子菜单的hideInMenu属性
  const processMenuItem = (route: any): any => {
    const isWorkspaceMenu = isWorkspaceRoute(route);
    const isCustomerMenu = isCustomerRoute(route);

    // 在客户列表模式下隐藏工作台菜单，在工作台模式下隐藏客户列表菜单
    const shouldHide = (isCustomerListMode && isWorkspaceMenu) ||
                     (!isCustomerListMode && isCustomerMenu);

    const processedRoute = {
      ...route,
      meta: {
        ...route.meta,
        hideInMenu: shouldHide || route.meta?.hideInMenu,
      },
    };



    // 递归处理子菜单
    if (processedRoute.children && processedRoute.children.length > 0) {
      processedRoute.children = processedRoute.children.map((child: any) => processMenuItem(child));
    }

    return processedRoute;
  };

  // 始终包含所有路由，但根据模式设置hideInMenu
  const allMenus = cloneDeep([...customerListMenu, ...workspaceMenus, ...localRoutes]);
  const menuList = allMenus.map(route => processMenuItem(route));

  // 总是重新生成完整的路由和菜单，确保 show 属性被正确设置
  const pageMap: ComponentRecordType = import.meta.glob('../views/**/*.vue');
  const layoutMap: ComponentRecordType = {
    BasicLayout,
    IFrameView,
    NotFoundComponent,
  };

  // 如果没有提供 router，我们只更新菜单，不更新路由
  if (router) {
    const { accessibleMenus, accessibleRoutes } = await generateAccessible(preferences.app.accessMode, {
      router,
      routes: accessRoutes,
      fetchMenuListAsync: async () => menuList,
      forbiddenComponent,
      layoutMap,
      pageMap,
    });

    // 更新菜单和路由
    accessStore.setAccessMenus(accessibleMenus);
    accessStore.setAccessRoutes(accessibleRoutes);
  } else {
    // 只更新菜单，使用 generateMenus 来确保 show 属性被正确设置
    const { generateMenus } = await import('@vben/utils');
    // 创建一个空的 router 对象来满足 generateMenus 的参数要求
    const mockRouter = { getRoutes: () => [] };
    const processedMenus = await generateMenus(menuList as any, mockRouter as any);
    accessStore.setAccessMenus(processedMenus);
  }
}

export { generateAccess };
