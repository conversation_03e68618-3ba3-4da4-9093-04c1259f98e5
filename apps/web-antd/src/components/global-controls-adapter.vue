<script lang="ts" setup>
  import { computed, onMounted } from 'vue';
  import { useRouter } from 'vue-router';

  import { GlobalControls } from '@vben/layouts';

  import { CopyOutlined } from '@ant-design/icons-vue';
  import { useClipboard } from '@vueuse/core';
  import { Button, message, Select } from 'ant-design-vue';

  import { ReturnIcon } from '#/assets/images';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { useMenuModeStore } from '#/store/modules/menu-mode';

  const router = useRouter();
  const menuModeStore = useMenuModeStore();

  // 使用全局公司选择hooks
  const {
    companyList,
    fetchCompanyNames,
    handleCompanyChange,
    handleMonthSelectChange,
    monthOptions,
    selectedCompany,
    selectedMonth,
  } = useCompanySelection();

  // 复制功能
  const { copy } = useClipboard({ legacy: true });

  // 是否显示全局控制组件（只在工作台模式下显示）
  const shouldShowControls = computed(() => {
    return menuModeStore.isWorkspaceMode();
  });

  // 公司选项
  const companyOptions = computed(() => {
    return companyList.value.map((company) => ({
      label: company.name,
      value: company.name,
    }));
  });

  // 过滤选项
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().includes(input.toLowerCase());
  };

  // 返回工作台（切换到客户列表模式）
  const handleBackToWorkspace = async () => {
    await menuModeStore.setMenuMode('customer-list');
    router.replace('/company-status');
  };

  // 处理公司变化
  const handleCompanySelectChange = (value: any) => {
    if (typeof value === 'string') {
      handleCompanyChange(value, () => {});
    }
  };

  // 处理月份变化
  const handleMonthChange = (value: any) => {
    if (typeof value === 'string') {
      handleMonthSelectChange(value, () => {});
    }
  };

  // 复制公司名称
  const handleCopyCompany = async () => {
    if (!selectedCompany.value) {
      message.warning('没有选择的公司');
      return;
    }

    try {
      await copy(selectedCompany.value);
      message.success('公司名称复制成功');
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  // 初始化时获取公司数据
  onMounted(() => {
    fetchCompanyNames();
  });
</script>

<template>
  <GlobalControls v-if="shouldShowControls" class="global-controls">
    <!-- 公司选择器 -->
    <div v-if="companyOptions.length > 0" class="company-selector reset-space">
      <div class="select-wrapper">
        <Select
          :value="selectedCompany"
          placeholder="选择公司"
          size="middle"
          show-search
          :options="companyOptions"
          :filter-option="filterOption"
          @change="handleCompanySelectChange"
          class="company-select"
        />
        <Button
          v-if="selectedCompany"
          type="text"
          size="small"
          class="copy-button-inside"
          @click="handleCopyCompany"
        >
          <CopyOutlined />
        </Button>
      </div>
    </div>

    <!-- 月份选择器 -->
    <div v-if="monthOptions.length > 0" class="month-selector reset-space">
      <Select
        :value="selectedMonth"
        placeholder="选择月份"
        style="width: 140px"
        size="middle"
        :options="monthOptions"
        @change="handleMonthChange"
        class="month-select"
      />
    </div>

    <!-- 返回工作台按钮 -->
    <Button
      type="primary"
      size="middle"
      @click="handleBackToWorkspace"
      class="back-button reset-space"
    >
      <img :src="ReturnIcon" class="return-icon" alt="返回" />
      返回客户列表
    </Button>
  </GlobalControls>
</template>

<style scoped>
  /* 小屏幕适配 */
  @media (max-width: 768px) {
    .company-select {
      width: 200px;
    }
  }

  @media (max-width: 480px) {
    .company-select {
      width: 150px;
    }
  }

  /* 小屏幕适配 */
  @media (max-width: 768px) {
    .company-select {
      width: 200px;
    }
  }

  @media (max-width: 480px) {
    .company-select {
      width: 150px;
    }
  }

  /* 小屏幕适配 */
  @media (max-width: 768px) {
    .company-select {
      width: 200px !important;
    }
  }

  @media (max-width: 480px) {
    .company-select {
      width: 150px !important;
    }
  }

  /* 全局控制组件样式 */
  .global-controls {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  /* 重置 Tailwind space 工具类 */
  .reset-space {
    margin: 0 !important;
  }

  .company-selector {
    display: flex;
    align-items: center;
  }

  .select-wrapper {
    position: relative;
    display: inline-block;
  }

  .copy-button-inside {
    position: absolute;
    top: 50%;
    right: 36px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding: 0;
    font-size: 12px;
    color: #8c8c8c;
    background: white;
    border: none;
    border-radius: 3px;
    box-shadow: none;
    opacity: 0;
    transition: opacity 0.2s ease;
    transform: translateY(-50%);
  }

  .select-wrapper:hover .copy-button-inside {
    opacity: 1;
  }

  .copy-button-inside:hover {
    color: #1890ff;
    background-color: #f0f8ff;
  }

  /* 响应式宽度设置 */
  .company-select {
    width: 320px;
  }

  .month-selector {
    display: flex;
    align-items: center;
  }

  .back-button {
    display: flex;
    align-items: center;
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
  }

  .return-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  /* 覆盖 Ant Design 的默认样式 */
  :deep(.company-select .ant-select-selector),
  :deep(.month-select .ant-select-selector) {
    height: 32px !important;
    font-size: 14px !important;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
  }

  /* 添加选项文字省略样式 */
  :deep(.company-select .ant-select-selection-item),
  :deep(.company-select .ant-select-item-option-content) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 覆盖 Tailwind space 工具类 */
  :deep(.global-controls > :not([hidden]) ~ :not([hidden])) {
    --tw-space-x-reverse: 0;

    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  /* 选择器内部文字样式 */
  :deep(.ant-select-selection-item) {
    font-size: 14px;
    line-height: 30px;
  }

  :deep(.ant-select-selection-placeholder) {
    font-size: 14px;
    line-height: 30px;
  }
</style>
