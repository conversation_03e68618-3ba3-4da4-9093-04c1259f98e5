<script setup lang="ts">
  import { computed, ref, watch } from 'vue';

  import { useDebounceFn, useDraggable } from '@vueuse/core';

  import { AiRobotPremium } from '#/assets/images';
  import { useMenuModeStore } from '#/store/modules/menu-mode';
  import aiChat from './AiChat.vue';

  const el = ref<HTMLElement | null>(null);
  const initialPosition = {
    x: window.innerWidth - 100,
    y: window.innerHeight - 100,
  };
  const threshold = 100;
  const snapDistance = 10; // 统一吸附距离
  const isAnimating = ref(false);

  // 自动吸附到边缘
  const handleEdgeSnap = () => {
    const { bottom, left, right, top } = isNearEdge.value;
    const elementWidth = el.value?.offsetWidth || 56;
    const elementHeight = el.value?.offsetHeight || 56;

    const viewWidth = document.documentElement.clientWidth;
    const viewHeight = document.documentElement.clientHeight;

    if (right) x.value = viewWidth - elementWidth - snapDistance;
    if (left) x.value = snapDistance;
    if (bottom) y.value = viewHeight - elementHeight - snapDistance * 2;
    if (top) y.value = snapDistance * 2;
  };

  const debouncedSnap = useDebounceFn(handleEdgeSnap, 100);
  const { style, x, y } = useDraggable(el, {
    initialValue: initialPosition,
    onMove: debouncedSnap,
    preventDefault: true,
  });

  // 计算图标是否靠近屏幕边缘
  const isNearEdge = computed(() => {
    return {
      bottom: y.value > window.innerHeight - threshold,
      left: x.value < threshold,
      right: x.value > window.innerWidth - threshold,
      top: y.value < threshold,
    };
  });

  // 监听窗口变化
  window.addEventListener('resize', debouncedSnap);
  const isHovered = ref(false);

  // 添加点击动画效果
  const drawerVisible = ref(false);
  const menuModeStore = useMenuModeStore();

  const handleClick = () => {
    isAnimating.value = true;
    drawerVisible.value = true;
    setTimeout(() => {
      isAnimating.value = false;
    }, 1000);
  };

  // 监听菜单模式变化，当切换到客户列表模式时自动关闭AI助手
  watch(
    () => menuModeStore.getCurrentMode(),
    (newMode) => {
      if (newMode === 'customer-list' && drawerVisible.value) {
        console.log('切换到客户列表模式，自动关闭AI助手');
        drawerVisible.value = false;
      }
    },
    { immediate: false }
  );
</script>

<template>
  <div>
    <div
      ref="el"
      :style="style"
      class="floating-ai-helper"
      @mouseenter="isHovered = true"
      @mouseleave="isHovered = false"
      @mouseup="handleEdgeSnap"
      @touchend="handleEdgeSnap"
      @click="handleClick"
    >
      <div
        class="ai-icon"
        :class="{
          'is-hovered': isHovered,
          'is-animating': isAnimating,
          'near-edge': Object.values(isNearEdge).some(Boolean),
        }"
      >
        <div class="icon-wrapper">
          <div class="icon-inner">
            <img :src="AiRobotPremium" />
          </div>
          <div class="icon-border"></div>
        </div>
        <div class="pulse-ring"></div>
        <div class="tech-circles">
          <div class="tech-circle"></div>
          <div class="tech-circle"></div>
          <div class="tech-circle"></div>
        </div>
      </div>
    </div>

    <a-drawer
      v-model:visible="drawerVisible"
      width="800"
      placement="right"
      class="ai-drawer"
    >
      <ai-chat />
    </a-drawer>
    <!-- existing floating icon template -->
  </div>
</template>

<style scoped lang="scss">
  .ai-drawer {
    :deep(.ant-drawer-body) {
      height: calc(100% - 55px);
      padding: 0;
    }
  }

  .floating-ai-helper {
    position: fixed;
    z-index: 1000;
    cursor: move;
    user-select: none;
  }

  .ai-icon {
    position: relative;
    width: 56px;
    height: 56px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.is-hovered {
      transform: scale(1.1);

      .pulse-ring {
        animation: pulse 1.5s cubic-bezier(0.24, 0, 0.38, 1) infinite;
      }

      .icon-wrapper {
        .icon-inner {
          background: linear-gradient(135deg, #00c6fb, #005bea);

          :deep(svg) {
            filter: drop-shadow(0 0 8px rgb(255 255 255 / 80%));
            transform: rotate(360deg);
          }
        }

        .icon-border {
          animation: rotate 4s linear infinite;
        }
      }

      .tech-circles {
        opacity: 1;

        .tech-circle {
          &:nth-child(1) {
            animation: tech-pulse 2s ease-in-out infinite;
          }

          &:nth-child(2) {
            animation: tech-pulse 2s ease-in-out infinite 0.4s;
          }

          &:nth-child(3) {
            animation: tech-pulse 2s ease-in-out infinite 0.8s;
          }
        }
      }
    }

    &.near-edge {
      .icon-wrapper {
        .icon-inner {
          box-shadow: 0 0 20px rgb(0 198 251 / 50%);
        }
      }
    }
  }

  .icon-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    .icon-inner {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #00c6fb, #005bea);
      border-radius: 50%;
      box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      :deep(svg) {
        width: 24px;
        height: 24px;
        color: #fff;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        transform: rotate(0);
      }
    }

    .icon-border {
      position: absolute;
      top: -2px;
      left: -2px;
      width: calc(100% + 4px);
      height: calc(100% + 4px);
      background: conic-gradient(
        from 0deg,
        rgb(0 198 251 / 0%),
        rgb(0 91 234 / 80%),
        rgb(0 198 251 / 0%)
      );
      border-radius: 50%;
      opacity: 0.6;
      transition: opacity 0.3s;
    }
  }

  .pulse-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #00c6fb, #005bea);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(1);
  }

  .tech-circles {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
    transform: translate(-50%, -50%);
  }

  .tech-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border: 1px solid rgb(0 198 251 / 20%);
    border-radius: 50%;
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);

    &:nth-child(1) {
      width: 140%;
      height: 140%;
    }

    &:nth-child(2) {
      width: 160%;
      height: 160%;
    }

    &:nth-child(3) {
      width: 180%;
      height: 180%;
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.7;
      transform: scale(0.95);
    }

    50% {
      opacity: 0.2;
      transform: scale(1.2);
    }

    100% {
      opacity: 0;
      transform: scale(0.95);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @keyframes tech-pulse {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }

    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }

    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(1.2);
    }
  }

  .ai-svg-icon {
    width: 28px;
    height: 28px;
    color: #fff;

    .brain-path {
      opacity: 0.9;
      stroke-dasharray: 100;
      stroke-dashoffset: 0;
      transition: stroke-dashoffset 0.6s ease;
    }

    .network-path {
      opacity: 0.85;
      stroke-dasharray: 60;
      stroke-dashoffset: 60;
      transition: stroke-dashoffset 0.4s ease;
    }

    .pulse-circle {
      opacity: 0.7;
      transition: transform 0.4s ease;
      transform-origin: center;
    }
  }

  .is-hovered {
    .ai-svg-icon {
      .brain-path {
        stroke-dashoffset: 100;
      }

      .network-path {
        stroke-dashoffset: 0;
      }

      .pulse-circle {
        animation: rotate 3s linear infinite;
      }
    }
  }

  .is-animating {
    .ai-svg-icon {
      .brain-path {
        stroke-dashoffset: -100;
        transition: stroke-dashoffset 0.8s ease;
      }

      .network-path {
        stroke-dashoffset: -60;
        transition: stroke-dashoffset 0.6s ease;
      }

      .pulse-circle {
        animation: pulse-rotate 1s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  @keyframes pulse-rotate {
    0% {
      transform: scale(1) rotate(0);
    }

    50% {
      transform: scale(1.2) rotate(180deg);
    }

    100% {
      transform: scale(1) rotate(360deg);
    }
  }
</style>
