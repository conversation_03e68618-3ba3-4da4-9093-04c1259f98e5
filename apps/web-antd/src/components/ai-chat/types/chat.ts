export interface CompanyInfo {
  id: string;
  name: string;
}

export interface UploadedFile {
  file_path: string;
  file_size: number;
  original_filename: string;
  saved_filename: string;
}

export interface WebSocketMessage {
  type: string;
  message?: string;
  task_id?: string;
  company_name?: string;
  month?: string;
  data?: any;
  progress?: number;
  status?: string;
  result?: any;
  sender?: string;
}

export interface TaskProgress {
  message: string;
  progress: number;
}

export interface MessageData {
  client_type: string;
  data: {
    company_name: string;
    files: Array<{
      location_type: string;
      url: string;
    }>;
    month: string;
  };
  task_type: string;
  type: string;
}

export interface ConnectionStatus {
  status: 'OPEN' | 'CONNECTING' | 'CLOSED' | 'CLOSING';
  isConnected: boolean;
  isConnecting: boolean;
  isClosed: boolean;
}

export interface ChatHeaderProps {
  companyList: CompanyInfo[];
  selectedCompany: string;
  selectedMonth: string;
  monthOptions: Array<{ label: string; value: string }>;
  connectionStatus: ConnectionStatus;
}

export interface FileUploadProps {
  isOpen: boolean;
  uploadedFiles: UploadedFile[];
  loading: boolean;
}

export interface MessageBubbleData {
  type: string;
  content: string;
  extraData?: any;
  thoughtChainItems?: any[];
  timestamp: Date;
  status?: string;
}
