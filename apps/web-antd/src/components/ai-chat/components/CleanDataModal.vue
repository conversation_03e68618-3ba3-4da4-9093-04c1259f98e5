<script setup lang="ts">
  import { computed, h, ref, watch } from 'vue';

  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { Button, Checkbox, Form, message, Modal } from 'ant-design-vue';

  interface Props {
    companyName: string;
    month: string;
    visible: boolean;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (
      e: 'confirm',
      data: { company_name: string; data_types: string[]; month: string },
    ): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 数据类型选项 - 重新排列为更合理的分组
  const specificDataTypes = [
    { category: 'invoice', icon: '📄', label: '销项发票', value: '销项发票' },
    { category: 'invoice', icon: '📋', label: '一般进项', value: '一般进项' },
    { category: 'invoice', icon: '🧾', label: '进项专票', value: '进项专票' },
    { category: 'bank', icon: '🏦', label: '银行回单', value: '银行回单' },
    { category: 'voucher', icon: '📊', label: '凭证', value: '凭证' },
  ];

  // 表单数据
  const formData = ref({
    dataTypes: [] as string[],
    selectAll: false,
  });

  // 计算属性：是否全选
  const isAllSelected = computed({
    get: () => formData.value.selectAll,
    set: (value: boolean) => {
      formData.value.selectAll = value;
      if (value) {
        // 全选时，选中所有具体类型
        formData.value.dataTypes = specificDataTypes.map((item) => item.value);
      } else {
        // 取消全选时，清空所有选择
        formData.value.dataTypes = [];
      }
    },
  });

  // 计算属性：是否部分选中
  const isIndeterminate = computed(() => {
    const selectedCount = formData.value.dataTypes.length;
    return selectedCount > 0 && selectedCount < specificDataTypes.length;
  });

  // 监听具体选项变化，更新全选状态
  watch(
    () => formData.value.dataTypes,
    (newVal) => {
      const selectedCount = newVal.length;
      if (selectedCount === specificDataTypes.length) {
        formData.value.selectAll = true;
      } else if (selectedCount === 0) {
        formData.value.selectAll = false;
      } else {
        formData.value.selectAll = false;
      }
    },
  );

  // 处理确认
  const handleConfirm = () => {
    if (formData.value.dataTypes.length === 0) {
      message.warning('请选择要清除的数据类型');
      return;
    }

    // 显示二次确认对话框
    Modal.confirm({
      cancelText: '取消',
      content: `确定要清除公司"${props.companyName}"在${props.month}月的${
        isAllSelected.value ? '全部数据' : formData.value.dataTypes.join('、')
      }吗？此操作不可恢复！`,
      icon: h(ExclamationCircleOutlined),
      okText: '确认清除',
      okType: 'danger',
      onOk() {
        // 如果是全选，只传"all"；否则传具体选中的类型
        const dataTypes = isAllSelected.value
          ? ['all']
          : formData.value.dataTypes;

        emit('confirm', {
          company_name: props.companyName,
          data_types: dataTypes,
          month: props.month,
        });

        handleCancel();
      },
      title: '确认清除数据',
    });
  };

  // 处理取消
  const handleCancel = () => {
    emit('update:visible', false);
    // 重置表单
    formData.value.dataTypes = [];
    formData.value.selectAll = false;
  };

  // 监听visible变化，重置表单
  watch(
    () => props.visible,
    (newVal) => {
      if (!newVal) {
        formData.value.dataTypes = [];
        formData.value.selectAll = false;
      }
    },
  );
</script>

<template>
  <Modal
    :open="visible"
    title="清除公司月度数据"
    width="500px"
    @cancel="handleCancel"
  >
    <div class="clean-data-modal">
      <div class="company-info">
        <p>
          <strong>公司名称：</strong>
          {{ companyName }}
        </p>
        <p>
          <strong>月份：</strong>
          {{ month }}
        </p>
      </div>

      <Form layout="vertical">
        <Form.Item label="选择要清除的数据类型：" required>
          <div class="checkbox-list">
            <!-- 全选选项 -->
            <Checkbox
              v-model:checked="isAllSelected"
              :indeterminate="isIndeterminate"
              class="all-option"
            >
              全部数据
            </Checkbox>

            <!-- 具体数据类型选项 -->
            <Checkbox.Group v-model:value="formData.dataTypes">
              <div class="checkbox-categories">
                <!-- 发票类数据 -->
                <div class="category-group">
                  <div class="category-title">
                    <span class="category-icon">📄</span>
                    发票数据
                  </div>
                  <div class="category-items">
                    <Checkbox
                      v-for="option in specificDataTypes.filter(
                        (item) => item.category === 'invoice',
                      )"
                      :key="option.value"
                      :value="option.value"
                      class="data-checkbox"
                    >
                      <span class="option-icon">{{ option.icon }}</span>
                      {{ option.label }}
                    </Checkbox>
                  </div>
                </div>

                <!-- 银行数据 -->
                <div class="category-group">
                  <div class="category-title">
                    <span class="category-icon">🏦</span>
                    银行数据
                  </div>
                  <div class="category-items">
                    <Checkbox
                      v-for="option in specificDataTypes.filter(
                        (item) => item.category === 'bank',
                      )"
                      :key="option.value"
                      :value="option.value"
                      class="data-checkbox"
                    >
                      <span class="option-icon">{{ option.icon }}</span>
                      {{ option.label }}
                    </Checkbox>
                  </div>
                </div>

                <!-- 凭证数据 -->
                <div class="category-group">
                  <div class="category-title">
                    <span class="category-icon">📊</span>
                    凭证数据
                  </div>
                  <div class="category-items">
                    <Checkbox
                      v-for="option in specificDataTypes.filter(
                        (item) => item.category === 'voucher',
                      )"
                      :key="option.value"
                      :value="option.value"
                      class="data-checkbox"
                    >
                      <span class="option-icon">{{ option.icon }}</span>
                      {{ option.label }}
                    </Checkbox>
                  </div>
                </div>
              </div>
            </Checkbox.Group>
          </div>
        </Form.Item>
      </Form>

      <div class="warning-text">
        <ExclamationCircleOutlined style="margin-right: 8px; color: #ff4d4f" />
        <span>警告：清除操作不可恢复，请谨慎操作！</span>
      </div>
    </div>

    <template #footer>
      <Button @click="handleCancel">取消</Button>
      <Button
        type="primary"
        danger
        @click="handleConfirm"
        :disabled="formData.dataTypes.length === 0"
      >
        确认清除
      </Button>
    </template>
  </Modal>
</template>

<style scoped>
  .clean-data-modal {
    padding: 16px 0;
  }

  .company-info {
    padding: 12px;
    margin-bottom: 20px;
    background: #f5f5f5;
    border-radius: 6px;
  }

  .company-info p {
    margin: 0;
    margin-bottom: 8px;
  }

  .company-info p:last-child {
    margin-bottom: 0;
  }

  .checkbox-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .checkbox-list .ant-checkbox-wrapper {
    margin: 0;
  }

  .all-option {
    padding-bottom: 12px;
    margin-bottom: 12px;
    font-weight: 600;
    color: #1890ff;
    border-bottom: 1px solid #d9d9d9;
  }

  .checkbox-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .category-group {
    padding: 16px;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .category-group:hover {
    background: #f6f9ff;
    border-color: #1890ff;
  }

  .category-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #262626;
  }

  .category-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  .category-items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px 20px;
    align-items: start;
  }

  /* 当发票数据有3个选项时，使用3列布局 */
  .category-group:first-child .category-items {
    grid-template-columns: repeat(3, 1fr);
  }

  .data-checkbox {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .data-checkbox:hover {
    background: rgb(24 144 255 / 6%);
  }

  .option-icon {
    margin-right: 6px;
    font-size: 14px;
  }

  .category-items .ant-checkbox-wrapper {
    width: 100%;
    margin: 0;
  }

  .warning-text {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-top: 16px;
    font-size: 14px;
    color: #ff4d4f;
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 6px;
  }
</style>
