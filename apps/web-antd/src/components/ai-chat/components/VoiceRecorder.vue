<script setup lang="ts">
import { nextTick, onUnmounted, ref } from 'vue';
import { Button, message } from 'ant-design-vue';
import { AudioOutlined, StopOutlined } from '@ant-design/icons-vue';

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'voiceRecorded', audioBlob: Blob): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<Emits>();

const isRecording = ref(false);
const recordingTime = ref(0);
const mediaRecorder = ref<MediaRecorder | null>(null);
const audioChunks = ref<Blob[]>([]);
const recordingTimer = ref<number | null>(null);

// 开始录音
const startRecording = async () => {
  try {
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        sampleRate: 16000,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      } 
    });

    // 创建MediaRecorder实例
    const recorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus',
    });

    mediaRecorder.value = recorder;
    audioChunks.value = [];
    isRecording.value = true;
    recordingTime.value = 0;

    // 开始录音定时器
    recordingTimer.value = window.setInterval(() => {
      recordingTime.value += 1;
    }, 1000);

    // 处理录音数据
    recorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data);
      }
    };

    // 录音结束处理
    recorder.onstop = async () => {
      const audioBlob = new Blob(audioChunks.value, { type: 'audio/webm' });
      
      // 转换为PCM格式
      try {
        const pcmBlob = await convertToPCM(audioBlob);
        emit('voiceRecorded', pcmBlob);
      } catch (error) {
        console.error('转换音频格式失败:', error);
        message.error('音频处理失败，请重试');
      }

      // 清理资源
      stream.getTracks().forEach(track => track.stop());
      isRecording.value = false;
      recordingTime.value = 0;
      if (recordingTimer.value) {
        clearInterval(recordingTimer.value);
        recordingTimer.value = null;
      }
    };

    // 开始录音
    recorder.start();
    message.success('开始录音...');
  } catch (error) {
    console.error('录音失败:', error);
    message.error('无法访问麦克风，请检查权限设置');
    isRecording.value = false;
  }
};

// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop();
  }
};

// 转换音频为PCM格式
const convertToPCM = async (audioBlob: Blob): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = async () => {
      try {
        const arrayBuffer = reader.result as ArrayBuffer;
        const audioContext = new AudioContext({ sampleRate: 16000 });
        
        // 解码音频数据
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // 转换为单声道
        const channelData = audioBuffer.getChannelData(0);
        
        // 重采样到16kHz（如果需要）
        const targetSampleRate = 16000;
        const resampledData = resampleAudio(channelData, audioBuffer.sampleRate, targetSampleRate);
        
        // 转换为16位PCM
        const pcmData = new Int16Array(resampledData.length);
        for (let i = 0; i < resampledData.length; i++) {
          pcmData[i] = Math.max(-32768, Math.min(32767, resampledData[i] * 32767));
        }
        
        // 创建PCM Blob
        const pcmBlob = new Blob([pcmData.buffer], { type: 'audio/pcm' });
        resolve(pcmBlob);
        
        audioContext.close();
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(reader.error);
    reader.readAsArrayBuffer(audioBlob);
  });
};

// 音频重采样函数
const resampleAudio = (audioData: Float32Array, originalSampleRate: number, targetSampleRate: number): Float32Array => {
  if (originalSampleRate === targetSampleRate) {
    return audioData;
  }
  
  const ratio = originalSampleRate / targetSampleRate;
  const newLength = Math.floor(audioData.length / ratio);
  const result = new Float32Array(newLength);
  
  for (let i = 0; i < newLength; i++) {
    const sourceIndex = i * ratio;
    const index = Math.floor(sourceIndex);
    const fraction = sourceIndex - index;
    
    if (index + 1 < audioData.length) {
      result[i] = audioData[index] * (1 - fraction) + audioData[index + 1] * fraction;
    } else {
      result[i] = audioData[index];
    }
  }
  
  return result;
};

// 格式化录音时间
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 清理资源
onUnmounted(() => {
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value);
  }
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop();
  }
});
</script>

<template>
  <div class="voice-recorder">
    <Button
      type="text"
      :loading="loading"
      :disabled="loading"
      @click="isRecording ? stopRecording() : startRecording()"
      :class="['recorder-button', { 'recording': isRecording }]"
      :title="isRecording ? `录音中 ${formatTime(recordingTime)}` : '语音输入'"
    >
      <AudioOutlined v-if="!isRecording" />
      <StopOutlined v-else />
    </Button>
  </div>
</template>

<style scoped lang="scss">
.voice-recorder {
  margin-right: 8px;
  
  .recorder-button {
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.recording {
      color: #ff4d4f;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 hsl(0deg 85% 63% / 70%);
  }

  70% {
    box-shadow: 0 0 0 8px hsl(0deg 85% 63% / 0%);
  }

  100% {
    box-shadow: 0 0 0 0 hsl(0deg 85% 63% / 0%);
  }
}
</style>