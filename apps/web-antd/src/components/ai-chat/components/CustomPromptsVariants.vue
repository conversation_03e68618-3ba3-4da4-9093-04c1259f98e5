<script setup lang="ts">
  import { computed } from 'vue';

  import { Button } from 'ant-design-vue';

  interface PromptItem {
    disabled?: boolean;
    icon?: any;
    key: string;
    label: string;
  }

  interface Props {
    className?: string;
    items: PromptItem[];
    onItemClick?: (info: { data: PromptItem }) => void;
    variant?: 'default' | 'glass' | 'minimal' | 'neon';
  }

  const props = withDefaults(defineProps<Props>(), {
    variant: 'default',
  });

  const handleClick = (item: PromptItem) => {
    if (item.disabled) return;
    props.onItemClick?.({ data: item });
  };

  const buttonItems = computed(() =>
    props.items.map((item) => ({
      ...item,
      onClick: () => handleClick(item),
    })),
  );

  const containerClass = computed(() => [
    'custom-prompts',
    `variant-${props.variant}`,
    props.className,
  ]);
</script>

<template>
  <div :class="containerClass">
    <Button
      v-for="item in buttonItems"
      :key="item.key"
      :disabled="item.disabled"
      class="prompt-button"
      :class="[{ disabled: item.disabled }]"
      @click="item.onClick"
    >
      <span v-if="item.icon" class="button-icon">
        <component :is="item.icon" />
      </span>
      <span class="button-label">{{ item.label }}</span>
    </Button>
  </div>
</template>

<style scoped lang="scss">
  /* 响应式设计 */
  @media (max-width: 768px) {
    .custom-prompts {
      gap: 6px;
      padding: 8px 0 12px;

      .prompt-button {
        min-width: 80px;
        max-width: 140px;
        height: 28px;
        padding: 4px 8px;
        font-size: 10px;
      }
    }
  }

  @media (max-width: 480px) {
    .custom-prompts {
      gap: 4px;
      padding: 6px 0 10px;

      .prompt-button {
        min-width: 70px;
        max-width: 120px;
        height: 26px;
        padding: 3px 6px;
        font-size: 9px;
      }
    }
  }

  @media (max-width: 360px) {
    .custom-prompts {
      gap: 3px;

      .prompt-button {
        min-width: 60px;
        max-width: 100px;
        height: 24px;
        padding: 2px 5px;
        font-size: 8px;
      }
    }
  }

  .custom-prompts {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    width: 100%;
    padding: 10px 0 14px;

    .prompt-button {
      position: relative;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      min-width: 85px;
      max-width: 160px;
      height: 32px;
      padding: 5px 10px;
      margin: 0;
      overflow: hidden;
      font-size: 11px;
      font-weight: 500;
      line-height: 1.2;
      text-align: center;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      .button-icon {
        flex-shrink: 0;
        margin-right: 4px;
        font-size: 12px;
        opacity: 0.7;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .button-label {
        flex: 1;
        overflow: hidden;
        font-size: 11px;
        font-weight: 500;
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: color 0.3s ease;
      }

      &:hover:not(.disabled) .button-icon {
        opacity: 1;
        transform: scale(1.15);
      }

      &:focus-visible {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }

  /* 默认样式变体 */
  .variant-default {
    .prompt-button {
      background: linear-gradient(135deg, #fff 0%, #f8fafc 50%, #f1f5f9 100%);
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      box-shadow:
        0 1px 2px rgb(0 0 0 / 4%),
        0 1px 3px rgb(0 0 0 / 6%);

      &:hover:not(.disabled) {
        color: #fff;
        background: linear-gradient(
          135deg,
          #3b82f6 0%,
          #2563eb 50%,
          #1d4ed8 100%
        );
        border-color: #3b82f6;
        box-shadow:
          0 4px 12px rgb(59 130 246 / 25%),
          0 2px 6px rgb(0 0 0 / 8%);
        transform: translateY(-2px) scale(1.02);
      }

      &:active:not(.disabled) {
        transform: translateY(0) scale(0.98);
      }

      &.disabled {
        color: #94a3b8;
        background: #f8fafc;
        border-color: #e2e8f0;
      }
    }
  }

  /* 极简样式变体 */
  .variant-minimal {
    .prompt-button {
      color: #64748b;
      background: transparent;
      border: 1px solid #e2e8f0;
      border-radius: 6px;

      &:hover:not(.disabled) {
        color: #334155;
        background: #f8fafc;
        border-color: #cbd5e1;
        transform: translateY(-1px);
      }

      &:active:not(.disabled) {
        transform: translateY(0);
      }

      &.disabled {
        color: #94a3b8;
        background: #f1f5f9;
        border-color: #e2e8f0;
      }
    }
  }

  /* 玻璃态样式变体 */
  .variant-glass {
    .prompt-button {
      color: #374151;
      background: rgb(255 255 255 / 10%);
      backdrop-filter: blur(10px);
      border: 1px solid rgb(255 255 255 / 20%);
      border-radius: 12px;

      &:hover:not(.disabled) {
        color: #1d4ed8;
        background: rgb(59 130 246 / 10%);
        border-color: rgb(59 130 246 / 30%);
        box-shadow: 0 8px 25px rgb(59 130 246 / 15%);
        transform: translateY(-2px);
      }

      &:active:not(.disabled) {
        transform: translateY(0);
      }

      &.disabled {
        color: #94a3b8;
        background: rgb(241 245 249 / 50%);
        border-color: rgb(226 232 240 / 50%);
      }
    }
  }

  /* 霓虹样式变体 */
  .variant-neon {
    .prompt-button {
      color: #e5e7eb;
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 8px;
      box-shadow: 0 0 0 1px rgb(59 130 246 / 10%);

      &:hover:not(.disabled) {
        color: #fff;
        background: #1e293b;
        border-color: #3b82f6;
        box-shadow:
          0 0 20px rgb(59 130 246 / 30%),
          0 0 40px rgb(59 130 246 / 10%),
          0 0 0 1px rgb(59 130 246 / 50%);
        transform: translateY(-2px);
      }

      &:active:not(.disabled) {
        box-shadow:
          0 0 10px rgb(59 130 246 / 20%),
          0 0 20px rgb(59 130 246 / 10%);
        transform: translateY(0);
      }

      &.disabled {
        color: #718096;
        background: #2d3748;
        border-color: #4a5568;
        box-shadow: none;
      }
    }
  }
</style>
