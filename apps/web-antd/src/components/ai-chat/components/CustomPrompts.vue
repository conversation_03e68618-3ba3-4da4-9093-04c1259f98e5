<script setup lang="ts">
  import { computed } from 'vue';

  import { Button } from 'ant-design-vue';

  interface PromptItem {
    disabled?: boolean;
    icon?: any;
    key: string;
    label: string;
  }

  interface Props {
    className?: string;
    items: PromptItem[];
    onItemClick?: (info: { data: PromptItem }) => void;
  }

  const props = defineProps<Props>();

  const handleClick = (item: PromptItem) => {
    if (item.disabled) return;
    props.onItemClick?.({ data: item });
  };

  const buttons = computed(() =>
    props.items.map((item) => ({
      ...item,
      onClick: () => handleClick(item),
    })),
  );
</script>

<template>
  <div class="custom-prompts" :class="[className]">
    <Button
      v-for="item in buttons"
      :key="item.key"
      :disabled="item.disabled"
      class="prompt-button"
      :class="[{ disabled: item.disabled }]"
      @click="item.onClick"
    >
      <span v-if="item.icon" class="button-icon">
        <component :is="item.icon" />
      </span>
      <span class="button-label">{{ item.label }}</span>
    </Button>
  </div>
</template>

<style scoped lang="scss">
  /* 添加旋转动画 */
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .custom-prompts {
      gap: 4px;
      padding: 0 0 6px;

      .prompt-button {
        min-width: 72px;
        max-width: 130px;
        height: 22px;
        padding: 3px 6px;
        font-size: 9px;

        .button-icon {
          font-size: 10px;
        }

        .button-label {
          font-size: 9px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .custom-prompts {
      gap: 3px;
      padding: 0 0 5px;

      .prompt-button {
        min-width: 68px;
        max-width: 120px;
        height: 20px;
        padding: 2px 5px;
        font-size: 8px;

        .button-icon {
          margin-right: 2px;
          font-size: 9px;
        }

        .button-label {
          font-size: 8px;
        }
      }
    }
  }

  /* 超小屏幕优化 */
  @media (max-width: 360px) {
    .custom-prompts {
      gap: 2px;
      padding: 0 0 4px;

      .prompt-button {
        min-width: 64px;
        max-width: 110px;
        height: 18px;
        padding: 2px 4px;
        font-size: 7px;

        .button-icon {
          margin-right: 2px;
          font-size: 8px;
        }

        .button-label {
          font-size: 7px;
        }
      }
    }
  }

  .custom-prompts {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    width: 100%;
    padding: 2px 0 10px;

    .prompt-button {
      position: relative;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      min-width: 76px;
      max-width: 140px;
      height: 24px;
      padding: 3px 6px;
      margin: 0;
      overflow: hidden;
      font-size: 10px;
      font-weight: 500;
      line-height: 1.2;
      text-align: center;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      background: linear-gradient(135deg, #fff 0%, #f8fafc 50%, #f1f5f9 100%);
      border: 1px solid #e2e8f0;
      border-radius: 4px;
      box-shadow:
        0 1px 2px rgb(0 0 0 / 4%),
        0 1px 1px rgb(0 0 0 / 3%);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &::before {
        position: absolute;
        inset: 0;
        pointer-events: none;
        content: '';
        background: linear-gradient(
          135deg,
          rgb(255 255 255 / 10%) 0%,
          transparent 50%
        );
        border-radius: 6px;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &:hover:not(.disabled) {
        color: #fff;
        background: linear-gradient(
          135deg,
          #3b82f6 0%,
          #2563eb 50%,
          #1d4ed8 100%
        );
        border-color: #3b82f6;
        box-shadow:
          0 2px 4px rgb(59 130 246 / 20%),
          0 1px 2px rgb(0 0 0 / 6%);
        transform: translateY(-1px);

        &::before {
          opacity: 1;
        }
      }

      &:active:not(.disabled) {
        box-shadow:
          0 1px 2px rgb(59 130 246 / 15%),
          0 1px 1px rgb(0 0 0 / 5%);
        transform: translateY(0);
      }

      &.disabled {
        color: #94a3b8;
        cursor: not-allowed;
        background: #f8fafc;
        border-color: #e2e8f0;
        box-shadow: none;
        opacity: 0.5;
      }

      .button-icon {
        flex-shrink: 0;
        margin-right: 3px;
        font-size: 11px;
        filter: grayscale(0.2);
        opacity: 0.7;
        transition: all 0.2s ease;
      }

      .button-label {
        flex: 1;
        overflow: hidden;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: color 0.2s ease;
      }

      &:hover:not(.disabled) .button-icon {
        filter: grayscale(0) brightness(1.1);
        opacity: 1;
        transform: scale(1.1);
      }

      &:focus-visible {
        outline: 2px solid #3b82f6;
        outline-offset: 1px;
      }

      &.loading {
        pointer-events: none;
        opacity: 0.7;

        .button-icon {
          animation: spin 1s linear infinite;
        }
      }
    }
  }
</style>
