<script setup lang="ts">
  import type { UploadFile } from 'ant-design-vue/es/upload/interface';

  import type { ActionType } from '../constants/actionTypes';
  import type { UploadedFile } from '../types/chat';

  import { computed, h, ref } from 'vue';

  import {
    CloudDownloadOutlined,
    DatabaseOutlined,
    FileTextOutlined,
    LinkOutlined,
    MergeCellsOutlined,
    SyncOutlined,
    TeamOutlined,
  } from '@ant-design/icons-vue';
  import { Button } from 'ant-design-vue';
  import { Sender, Suggestion } from 'ant-design-x-vue';

  import { ActionHandlers, ActionTypes } from '../constants/actionTypes';
  import CustomPrompts from './CustomPrompts.vue';
  import FileUpload from './FileUpload.vue';
  import VoiceRecorder from './VoiceRecorder.vue';

  interface Props {
    fileItems: UploadFile[];
    fileUploadOpen: boolean;
    inputValue: string;
    loading: boolean;
    uploadedFiles: UploadedFile[];
  }

  interface Emits {
    (e: 'update:inputValue', value: string): void;
    (e: 'update:fileUploadOpen', value: boolean): void;
    (e: 'submit', content: string): void;
    (e: 'fileChange', data: { fileList: UploadFile[] }): void;
    (e: 'fileUpload', firstFile: File, fileList: FileList): void;
    (e: 'promptSelect', info: { data: any }): void;
    (e: 'suggestionSelect', itemVal: string): void;
    (e: 'syncData', content: string): void;
    (e: 'syncSubjectConfig', content: string): void;
    (e: 'syncCustomerList', content: string): void;
    (e: 'dbToVoucher', content: string): void;
    (e: 'mergeVouchers', content: string): void;
    (e: 'invoiceFileDownload', invoiceType: string): void;
    (e: 'bankReceiptSync'): void;
    (e: 'bankReceiptToVoucher', content: string): void;
    (e: 'cleanCompanyMonthData', content: string): void;
    (e: 'voiceRecorded', audioBlob: Blob): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const inputRef = ref();
  const fileUploadRef = ref();

  // 计算是否可以发送消息（有文本内容或有附件时可以发送）
  const canSendMessage = computed(() => {
    const hasContent = props.inputValue.trim().length > 0;
    const hasFiles = props.uploadedFiles.length > 0;
    const result = hasContent || hasFiles;

    // 只在状态变化时输出调试信息
    if (hasFiles && !hasContent) {
      console.log('✅ 检测到附件，允许发送（无文本内容）');
    }

    return result;
  });

  // 自定义发送按钮 - 处理有附件但没有文本的情况
  const customActions = computed(() => {
    return (_oriNode: any, { components }: any) => {
      const { SendButton } = components;

      return [
        h(VoiceRecorder, {
          loading: props.loading,
          onVoiceRecorded: handleVoiceRecorded,
        }),
        h(
          SendButton,
          {
            disabled: !canSendMessage.value,
            loading: props.loading,
            onClick: (e: Event) => {
              // 只有在有附件但没有文本内容时才自定义处理
              const hasContent = props.inputValue.trim().length > 0;
              const hasFiles = props.uploadedFiles.length > 0;

              if (hasFiles && !hasContent) {
                // 有附件但没有文本内容，需要自定义处理
                e.preventDefault();
                e.stopPropagation();
                console.log('🚀 发送附件（无文本）');
                handleSubmit('');
              }
              // 如果有文本内容，让 Sender 组件自己处理（不阻止默认行为）
            },
          },
          '发送',
        ),
      ];
    };
  });

  interface SuggestionItem {
    label: string;
    value: string;
  }

  type SuggestionItems = SuggestionItem[];

  const suggestions: SuggestionItems = [
    {
      label: ActionTypes.SYNC_INVOICE_DATA,
      value: ActionTypes.SYNC_INVOICE_DATA,
    },
    {
      label: ActionTypes.SYNC_SUBJECT_CONFIG,
      value: ActionTypes.SYNC_SUBJECT_CONFIG,
    },
    {
      label: ActionTypes.SYNC_CUSTOMER_LIST,
      value: ActionTypes.SYNC_CUSTOMER_LIST,
    },
    {
      label: ActionTypes.INVOICE_TO_VOUCHER,
      value: ActionTypes.INVOICE_TO_VOUCHER,
    },
    { label: ActionTypes.MERGE_VOUCHERS, value: ActionTypes.MERGE_VOUCHERS },
    {
      label: ActionTypes.SYNC_INVOICE_FILES,
      value: ActionTypes.SYNC_INVOICE_FILES,
    },
    {
      label: ActionTypes.SYNC_BANK_RECEIPT,
      value: ActionTypes.SYNC_BANK_RECEIPT,
    },
    {
      label: ActionTypes.BANK_RECEIPT_TO_VOUCHER,
      value: ActionTypes.BANK_RECEIPT_TO_VOUCHER,
    },
    {
      label: ActionTypes.CLEAN_COMPANY_MONTH_DATA,
      value: ActionTypes.CLEAN_COMPANY_MONTH_DATA,
    },
  ];

  const promptTemplates = [
    {
      icon: h(SyncOutlined, { style: { color: '#52C41A' } }),
      key: '1',
      label: ActionTypes.SYNC_INVOICE_DATA,
    },
    {
      icon: h(DatabaseOutlined, { style: { color: '#FA8C16' } }),
      key: '2',
      label: ActionTypes.SYNC_SUBJECT_CONFIG,
    },
    {
      icon: h(TeamOutlined, { style: { color: '#13C2C2' } }),
      key: '3',
      label: ActionTypes.SYNC_CUSTOMER_LIST,
    },
    {
      icon: h(FileTextOutlined, { style: { color: '#1890FF' } }),
      key: '4',
      label: ActionTypes.INVOICE_TO_VOUCHER,
    },
    {
      icon: h(MergeCellsOutlined, { style: { color: '#FF6B35' } }),
      key: '5',
      label: ActionTypes.MERGE_VOUCHERS,
    },
    {
      icon: h(CloudDownloadOutlined, { style: { color: '#722ED1' } }),
      key: '6',
      label: ActionTypes.SYNC_INVOICE_FILES,
    },
    {
      icon: h(CloudDownloadOutlined, { style: { color: '#1890FF' } }),
      key: '7',
      label: ActionTypes.SYNC_BANK_RECEIPT,
    },
    {
      icon: h(FileTextOutlined, { style: { color: '#52C41A' } }),
      key: '8',
      label: ActionTypes.BANK_RECEIPT_TO_VOUCHER,
    },
  ];

  const handleSubmit = (content: string) => {
    // 检查是否可以发送消息（有文本内容或有附件）
    if (!canSendMessage.value) {
      console.log('❌ 无法发送：没有内容也没有附件');
      return;
    }

    console.log('✅ 发送消息:', {
      content: content || '(仅附件)',
      hasFiles: props.uploadedFiles.length > 0,
    });

    emit('submit', content);
  };

  const handleInputChange = (value: string) => {
    emit('update:inputValue', value);
  };

  const handleFileUploadOpenChange = (open: boolean) => {
    emit('update:fileUploadOpen', open);
  };

  const handleFileChange = (data: { fileList: UploadFile[] }) => {
    emit('fileChange', data);
  };

  const handleFileUpload = (firstFile: File, fileList: FileList) => {
    emit('fileUpload', firstFile, fileList);
  };

  const handleVoiceRecorded = (audioBlob: Blob) => {
    emit('voiceRecorded', audioBlob);
  };

  // 统一的事件处理函数
  const handleAction = (action: ActionType) => {
    const handler = ActionHandlers[action];
    if (handler) {
      (emit as any)(handler.event, handler.payload || '');
    } else {
      // 对于未定义的动作，使用默认处理
      emit('suggestionSelect', action);
    }
  };

  const handlePromptSelect = (info: { data: any }) => {
    handleAction(info.data.label as ActionType);
  };

  const handleSuggestionSelect = (itemVal: string) => {
    handleAction(itemVal as ActionType);
  };

  const handleSenderChange = (
    nextVal: string,
    { onTrigger }: { onTrigger?: (show?: boolean) => void } = {},
  ) => {
    if (nextVal === '/') {
      onTrigger?.();
    } else if (!nextVal) {
      onTrigger?.(false);
    }
    handleInputChange(nextVal);
  };

  const toggleFileUpload = () => {
    handleFileUploadOpenChange(!props.fileUploadOpen);
  };

  defineExpose({
    fileUploadRef,
    inputRef,
  });
</script>

<template>
  <div class="chat-input">
    <CustomPrompts
      :items="promptTemplates"
      :on-item-click="handlePromptSelect"
    />

    <div class="sender-container">
      <Suggestion :items="suggestions" @select="handleSuggestionSelect">
        <template #default="{ onTrigger }: any">
          <Sender
            :value="inputValue"
            ref="inputRef"
            placeholder="输入消息，按 Enter 发送..."
            :loading="loading"
            :actions="customActions"
            @submit="handleSubmit"
            @paste-file="handleFileUpload"
            @change="(val) => handleSenderChange(val, { onTrigger })"
          >
            <template #prefix>
              <Button
                type="text"
                :icon="h(LinkOutlined)"
                @click="toggleFileUpload"
              />
            </template>

            <template #header>
              <FileUpload
                ref="fileUploadRef"
                :is-open="fileUploadOpen"
                :items="fileItems"
                :loading="loading"
                @update:is-open="handleFileUploadOpenChange"
                @file-change="handleFileChange"
                @file-upload="handleFileUpload"
              />
            </template>
          </Sender>
        </template>
      </Suggestion>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .chat-input {
    .sender-container {
      width: 100%;
    }
  }
</style>
