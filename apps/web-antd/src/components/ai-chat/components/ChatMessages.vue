<script setup lang="ts">
  import type { BubbleListProps } from 'ant-design-x-vue';

  import type { ChatMessage } from '#/store/modules/ai-chat';

  import { computed, h } from 'vue';

  import { UserOutlined } from '@ant-design/icons-vue';
  import { <PERSON><PERSON>, Flex } from 'ant-design-vue';
  import { Attachments, Bubble, Prompts, Welcome } from 'ant-design-x-vue';

  import { AiRobotCute, UserAvatarCute } from '#/assets/images';

  import MessageBubble from './MessageBubble.vue';

  interface Props {
    initializationError: null | string;
    isInitializing: boolean;
    loading: boolean;
    messages: ChatMessage[];
  }

  interface Emits {
    (e: 'showMessageDetail', message: ChatMessage): void;
    (e: 'reloadPage'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const showMessageDetail = (message: ChatMessage) => {
    emit('showMessageDetail', message);
  };

  const reloadPage = () => {
    emit('reloadPage');
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const bubbleItems = computed(() =>
    props.messages.map((msg) => {
      const lastMessage = props.messages[props.messages.length - 1];
      const isLoading =
        msg.type !== 'user' &&
        lastMessage &&
        lastMessage.id === msg.id &&
        props.loading;

      return {
        content: h(MessageBubble, {
          isLoading,
          message: msg,
          onShowDetail: showMessageDetail,
        }),
        key: msg.id,
        loading: isLoading,
        meta: formatTime(msg.timestamp),
        role: msg.type,
        shape: 'corner' as const,
        status: msg.status,
        type: msg.type,
        typing: msg.type === 'ai' ? { interval: 50, step: 2 } : undefined,
      };
    }),
  );

  // 基础角色配置
  const baseRoles: BubbleListProps['roles'] = {
    file: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items: any) =>
        h(
          Flex,
          { gap: 'middle', vertical: true },
          items.map((item: any) =>
            h(Attachments.FileCard, { item, key: item.uid }),
          ),
        ),
      placement: 'start',
      variant: 'borderless',
    },
    image: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items: any) =>
        h(
          'div',
          { style: { display: 'flex', flexDirection: 'column', gap: '8px' } },
          items.map((item: any) =>
            h('img', {
              alt: item.name,
              key: item.uid,
              src: item.url,
              style: { borderRadius: '4px', maxWidth: '300px' },
            }),
          ),
        ),
      placement: 'start',
      variant: 'borderless',
    },
    suggestion: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items) =>
        h(Prompts, { vertical: true, items: items as any }),
      placement: 'start',
      variant: 'borderless',
    },
    user: {
      avatar: {
        src: UserAvatarCute,
        style: {
          alignItems: 'center',
          background: 'transparent',
          border: 'none',
          borderRadius: '50%',
          boxShadow: 'none',
          display: 'flex',
          justifyContent: 'center',
          margin: '0',
          overflow: 'hidden',
          padding: '0',
        },
      },
      placement: 'end',
    },
  };

  // 动态生成roles配置
  const roles = computed(() => {
    const dynamicRoles: BubbleListProps['roles'] = { ...baseRoles };

    // 为所有消息类型动态添加AI角色配置
    props.messages.forEach((msg) => {
      if (msg.type !== 'user' && !dynamicRoles[msg.type]) {
        dynamicRoles[msg.type] = {
          avatar: {
            src: AiRobotCute,
            style: {
              alignItems: 'center',
              background: 'transparent',
              border: 'none',
              borderRadius: '50%',
              boxShadow: 'none',
              display: 'flex',
              justifyContent: 'center',
              margin: '0',
              overflow: 'hidden',
              padding: '0',
            },
          },
          placement: 'start',
          typing: true,
        };
      }
    });

    return dynamicRoles;
  });
</script>

<template>
  <div class="chat-messages">
    <!-- 初始化加载状态 -->
    <div v-if="isInitializing" class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在初始化AI助手...</p>
      </div>
    </div>

    <!-- 初始化错误状态 -->
    <div v-else-if="initializationError" class="error-container">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <p class="error-text">{{ initializationError }}</p>
        <Button type="primary" @click="reloadPage">重新加载</Button>
      </div>
    </div>

    <!-- 正常状态 -->
    <div v-else-if="messages.length === 0" class="welcome-container">
      <Welcome
        :class-names="{
          title: 'welcome-title',
          description: 'welcome-description',
        }"
        icon="https://cdn-icons-png.flaticon.com/128/8943/8943377.png"
      >
        <template #title>精算家AI会计助手</template>
        <template #description>
          <div class="welcome-desc-content">
            <p class="main-desc">专业的AI助手，帮您快速生成准确的会计凭证</p>
            <p class="sub-desc">上传财务文件，即可自动识别并生成凭证</p>
          </div>
        </template>
      </Welcome>
    </div>

    <Bubble.List
      v-else
      :items="bubbleItems"
      :roles="roles"
      :style="{ padding: '8px' }"
      class="bubble-list"
    />
  </div>
</template>

<style scoped lang="scss">
  .chat-messages {
    height: 100%;
    overflow-y: auto;
  }

  .loading-container,
  .error-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    padding: 32px !important;
  }

  .loading-content,
  .error-content {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  .loading-spinner {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 16px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #667eea !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
  }

  .loading-text {
    margin: 0 !important;
    font-size: 14px !important;
    color: #666 !important;
  }

  .error-icon {
    margin-bottom: 16px !important;
    font-size: 32px !important;
  }

  .error-text {
    margin: 0 0 16px !important;
    font-size: 14px !important;
    color: #ff4d4f !important;
  }

  .welcome-container {
    padding: 16px !important;

    :deep(.welcome-title) {
      // margin-bottom: 10px !important;
      font-size: 16px !important;
      font-weight: 600 !important;
      color: #2d3748 !important;
      // text-align: center !important;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
    }

    :deep(.welcome-description) {
      font-size: 12px !important;
      line-height: 1.5 !important;
      color: #4a5568 !important;
      // text-align: center !important;

      .welcome-desc-content {
        .main-desc {
          margin-bottom: 6px !important;
          font-size: 13px !important;
          font-weight: 500 !important;
          line-height: 1.4 !important;
          color: #333 !important;
        }

        .sub-desc {
          margin: 0 !important;
          font-size: 12px !important;
          color: #666 !important;
        }
      }
    }
  }

  .bubble-list {
    :deep(.ant-welcome) {
      padding: 20px !important;
      text-align: center !important;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
      border: 1px solid rgb(255 255 255 / 60%) !important;
      border-radius: 12px !important;
      box-shadow: 0 4px 16px rgb(0 0 0 / 8%) !important;
    }

    :deep(.ant-welcome-icon) {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 40px !important;
      height: 40px !important;
      margin-bottom: 12px !important;
    }

    // 头像容器样式优化
    :deep(.ant-bubble-avatar) {
      padding: 0 !important;
      margin: 0 !important;

      img {
        width: 100% !important;
        height: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        object-fit: cover !important;
        border-radius: 50% !important;
      }
    }

    // 确保头像图片完全填充容器
    :deep(.ant-avatar) {
      padding: 0 !important;
      margin: 0 !important;

      img {
        width: 100% !important;
        height: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        object-fit: cover !important;
        border-radius: 50% !important;
      }
    }

    // 针对BubbleList中的头像进行特殊处理
    :deep(.ant-bubble-list-item) {
      .ant-avatar {
        padding: 0 !important;
        margin: 0 !important;

        img {
          width: 100% !important;
          height: 100% !important;
          padding: 0 !important;
          margin: 0 !important;
          object-fit: cover !important;
        }
      }
    }

    // 调整聊天气泡间距 - 使用更强的选择器和更小的间距
    :deep(.ant-bubble) {
      gap: 6px !important;
      column-gap: 6px !important;
    }

    // 更具体的选择器确保样式生效
    :deep(.ant-bubble-list .ant-bubble) {
      gap: 6px !important;
      column-gap: 6px !important;
    }

    // 针对具体的气泡列表项
    :deep(.ant-bubble-list-item .ant-bubble) {
      gap: 6px !important;
      column-gap: 6px !important;
    }

    // 使用CSS类名选择器（如果上面的不生效）
    :deep(.css-kat34v.ant-bubble) {
      gap: 6px !important;
      column-gap: 6px !important;
    }

    // 最强选择器 - 直接覆盖内联样式
    :deep([class*='ant-bubble']) {
      gap: 6px !important;
      column-gap: 6px !important;
    }

    // 使用属性选择器强制覆盖
    :deep(div[class*='ant-bubble'][style*='column-gap']) {
      gap: 6px !important;
      column-gap: 6px !important;
    }

    // 最终方案：使用CSS变量覆盖
    :global(.ant-bubble) {
      --ant-bubble-gap: 6px !important;

      gap: 6px !important;
      column-gap: 6px !important;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
