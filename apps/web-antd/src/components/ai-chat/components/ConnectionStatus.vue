<script setup lang="ts">
import { Button } from 'ant-design-vue';
import type { ConnectionStatus } from '../types/chat';

interface Props {
  status: ConnectionStatus;
}

interface Emits {
  (e: 'reconnect'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const handleReconnect = () => {
  emit('reconnect');
};
</script>

<template>
  <div class="connection-status" :class="status.status.toLowerCase()">
    <div class="status-dot"></div>
    <span class="status-text">
      {{
        status.isConnected
          ? '已连接'
          : status.isConnecting
            ? '连接中'
            : '未连接'
      }}
    </span>
    <!-- 重连按钮，只在断开连接时显示 -->
    <Button
      v-if="status.isClosed"
      type="text"
      size="small"
      @click="handleReconnect"
      class="reconnect-btn"
      title="点击重连"
    >
      🔄
    </Button>
  </div>
</template>

<style scoped lang="scss">
// 样式由父组件AiChat.vue统一管理
</style>
