/**
 * Vite插件：确保config.json文件独立复制到构建输出目录
 * 该插件确保config.json文件不会被压缩或合并，保持独立状态
 */

import type { PluginOption } from 'vite';

interface ConfigCopyPluginOptions {
  /**
   * 是否在开发模式下也复制文件
   * @default false
   */
  copyInDev?: boolean;

  /**
   * 源配置文件路径（相对于项目根目录）
   * @default 'public/config.json'
   */
  source?: string;

  /**
   * 目标文件名
   * @default 'config.json'
   */
  target?: string;
}

export function configCopyPlugin(
  options: ConfigCopyPluginOptions = {},
): PluginOption {
  const {
    copyInDev = false,
    source = 'public/config.json',
    target = 'config.json',
  } = options;

  return {
    configResolved(config) {
      // 在开发模式下，config.json已经在public目录中，Vite会自动提供
      // 所以通常不需要额外处理
      if (config.command === 'serve' && !copyInDev) {}
    },

    configureServer(server) {
      // 开发服务器中不需要特殊处理，因为public目录中的文件会自动提供服务
      console.log(`🔧 配置文件 ${source} 将在开发模式下自动提供服务`);
    },

    generateBundle(options, bundle) {
      // 在构建时，确保config.json被单独复制
      try {
        // 读取配置文件内容 - 使用Vite的fs助手而不是直接使用Node.js API
        const { readFileSync } = require('node:fs');
        const { join } = require('node:path');
        const sourcePath = join(process.cwd(), source);

        try {
          const configContent = readFileSync(sourcePath, 'utf-8');

          // 将配置文件作为独立资源添加到bundle中
          this.emitFile({
            fileName: target,
            source: configContent,
            type: 'asset',
          });

          console.log(`✅ 配置文件已复制: ${source} -> ${target}`);
        } catch (error) {
          console.error(`❌ 读取配置文件失败: ${sourcePath}`, error);
        }
      } catch (error) {
        console.error(`❌ 复制配置文件失败:`, error);
      }
    },

    name: 'config-copy-plugin',
  };
}

export default configCopyPlugin;
