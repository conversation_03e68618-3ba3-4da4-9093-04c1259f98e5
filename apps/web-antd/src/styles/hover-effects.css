/* 通用悬浮效果样式库 */

/* 基础悬浮效果 */
.hover-lift {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

/* 轻微悬浮效果 */
.hover-lift-subtle {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift-subtle:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
}

/* 缩放悬浮效果 */
.hover-scale {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale-lg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale-lg:hover {
  transform: scale(1.1);
}

/* 按钮悬浮效果 */
.hover-btn-primary {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgb(59 130 246 / 20%);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-btn-primary::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  content: '';
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.hover-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgb(59 130 246 / 30%);
}

.hover-btn-primary:hover::before {
  left: 100%;
}

.hover-btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgb(59 130 246 / 25%);
}

/* 卡片悬浮效果 */
.hover-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
}

.hover-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 8px 25px rgb(0 0 0 / 8%);
  transform: translateY(-2px);
}

/* 输入框悬浮效果 */
.hover-input {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
}

.hover-input:hover {
  border-color: #94a3b8;
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
  transform: translateY(-1px);
}

.hover-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 12%), 0 2px 8px rgb(0 0 0 / 8%);
  transform: translateY(-1px);
}

/* 表格行悬浮效果 */
.hover-table-row {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-table-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
  transform: translateY(-1px);
}

/* 图标悬浮效果 */
.hover-icon {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 50%;
}

.hover-icon:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgb(0 0 0 / 15%));
}

/* 进度环悬浮效果 */
.hover-progress-ring {
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-progress-ring:hover {
  z-index: 10;
  transform: scale(1.15);
}

.hover-progress-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 状态点悬浮效果 */
.hover-status-dot {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.hover-status-dot:hover {
  transform: scale(1.3);
  box-shadow: 0 3px 12px rgb(0 0 0 / 20%);
}

/* 文本悬浮效果 */
.hover-text {
  transition: color 0.2s ease;
  cursor: pointer;
}

.hover-text:hover {
  color: #3b82f6;
}

/* 标签悬浮效果 */
.hover-tag {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.hover-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgb(0 0 0 / 15%);
}

/* 分页器悬浮效果 */
.hover-pagination-item {
  transition: all 0.2s ease;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.hover-pagination-item:hover {
  background: #f0f9ff;
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgb(59 130 246 / 15%);
  transform: translateY(-1px);
}

/* 动画关键帧 */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 特殊效果类 */
.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  animation: pulse-glow 1.5s ease-in-out infinite;
}

.hover-shimmer {
  position: relative;
  overflow: hidden;
}

.hover-shimmer::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  content: '';
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: none;
}

.hover-shimmer:hover::before {
  animation: shimmer 0.6s ease-out;
}

/* 可访问性支持 */
@media (prefers-reduced-motion: reduce) {
  .hover-lift,
  .hover-lift-subtle,
  .hover-scale,
  .hover-scale-lg,
  .hover-btn-primary,
  .hover-card,
  .hover-input,
  .hover-table-row,
  .hover-icon,
  .hover-progress-ring,
  .hover-status-dot,
  .hover-text,
  .hover-tag,
  .hover-pagination-item,
  .hover-glow,
  .hover-shimmer {
    transition: none;
  }
  
  .hover-lift:hover,
  .hover-lift-subtle:hover,
  .hover-scale:hover,
  .hover-scale-lg:hover,
  .hover-btn-primary:hover,
  .hover-card:hover,
  .hover-input:hover,
  .hover-table-row:hover,
  .hover-icon:hover,
  .hover-progress-ring:hover,
  .hover-status-dot:hover,
  .hover-tag:hover,
  .hover-pagination-item:hover {
    transform: none;
  }
  
  .hover-glow:hover {
    animation: none;
  }
  
  .hover-shimmer:hover::before {
    animation: none;
  }
}
