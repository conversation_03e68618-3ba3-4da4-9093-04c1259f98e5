import { ref, nextTick } from 'vue';

export function useColumnWidths() {
  // 用于列宽调整后强制重新渲染
  const resizeCounter = ref(0);

  // 保存列宽设置到本地存储
  const saveColumnWidths = (columns: any[], tabKey: string) => {
    try {
      const widths: Record<string, number> = {};
      columns.forEach((col) => {
        if (col.key && col.width) {
          widths[col.key] = col.width;
        }
      });
      const storageKey = `voucher-original-column-widths-${tabKey}`;
      localStorage.setItem(storageKey, JSON.stringify(widths));
    } catch (error) {
      console.warn('保存列宽设置失败:', error);
    }
  };

  // 从本地存储加载列宽设置
  const loadColumnWidths = (columns: any[], tabKey: string) => {
    try {
      const storageKey = `voucher-original-column-widths-${tabKey}`;
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        const widths = JSON.parse(saved);
        columns.forEach((col) => {
          if (col.key && widths[col.key]) {
            col.width = widths[col.key];
          }
        });

        // 强制刷新表格以应用新的列宽
        nextTick(() => {
          resizeCounter.value++;
        });

        return true;
      }
    } catch (error) {
      console.warn('加载列宽设置失败:', error);
    }
    return false;
  };

  // 处理列宽调整
  const handleResizeColumn = (
    w: number, 
    col: any, 
    columns: any[], 
    tabKey: string
  ) => {
    // 必须更新列宽，否则拖拽不会生效
    col.width = w;

    // 同时更新columns中对应的列
    const targetColumn = columns.find((c) => c.key === col.key);
    if (targetColumn) {
      targetColumn.width = w;
    }

    // 保存到本地存储
    saveColumnWidths(columns, tabKey);
  };

  // 清除所有列宽设置
  const clearColumnWidths = (tabKey?: string) => {
    try {
      if (tabKey) {
        const storageKey = `voucher-original-column-widths-${tabKey}`;
        localStorage.removeItem(storageKey);
      } else {
        // 清除所有相关的列宽设置
        const keys = Object.keys(localStorage).filter(key => 
          key.startsWith('voucher-original-column-widths-')
        );
        keys.forEach(key => localStorage.removeItem(key));
      }
      
      // 强制刷新表格
      nextTick(() => {
        resizeCounter.value++;
      });
    } catch (error) {
      console.warn('清除列宽设置失败:', error);
    }
  };

  return {
    resizeCounter,
    saveColumnWidths,
    loadColumnWidths,
    handleResizeColumn,
    clearColumnWidths,
  };
}
