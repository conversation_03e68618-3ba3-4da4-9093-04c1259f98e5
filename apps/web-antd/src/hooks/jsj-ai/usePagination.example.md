# usePagination Hook 使用示例

## 基础用法

```typescript
import { usePagination } from '#/hooks/jsj-ai/usePagination';

// 基础分页功能
const { currentPage, pageSize } = usePagination({
  storageKey: 'my-page-pagination',
});
```

## 带搜索重置的分页

```typescript
const searchKeyword = ref('');
const statusFilter = ref('all');

const { currentPage, pageSize } = usePagination({
  storageKey: 'my-page-pagination',
  defaultPage: 1,
  defaultPageSize: 20,
  resetTriggers: [searchKeyword, statusFilter], // 搜索或筛选时自动重置到第1页
});
```

## 带滚动位置记忆的分页

```typescript
const { currentPage, pageSize, saveScrollPosition, restoreScrollPosition } = usePagination({
  storageKey: 'my-page-pagination',
  defaultPage: 1,
  defaultPageSize: 20,
  resetTriggers: [searchKeyword, statusFilter],
  rememberScrollPosition: true, // 启用滚动位置记忆
  scrollContainer: '.table-wrapper', // 指定滚动容器选择器
});

// 或者使用 ref
const tableRef = ref<HTMLElement>();
const { currentPage, pageSize } = usePagination({
  storageKey: 'my-page-pagination',
  rememberScrollPosition: true,
  scrollContainer: tableRef, // 使用 ref
});
```

## 完整配置示例

```typescript
const {
  currentPage,
  pageSize,
  resetPagination,
  savePaginationState,
  restorePaginationState,
  saveScrollPosition,
  restoreScrollPosition,
  resetScrollPosition
} = usePagination({
  storageKey: 'company-status-pagination',
  defaultPage: 1,
  defaultPageSize: 20,
  enableStorage: true, // 是否启用本地存储
  resetTriggers: [searchKeyword, statusFilter],
  rememberScrollPosition: true, // 记住滚动位置
  scrollContainer: '.table-wrapper', // 滚动容器
  resetScrollOnPageChange: true, // 切换页码时重置滚动位置（默认 true）
});
```

## 手动控制

```typescript
// 手动重置分页
resetPagination();

// 手动保存状态
savePaginationState();

// 手动恢复状态
restorePaginationState();

// 手动保存滚动位置
saveScrollPosition();

// 手动恢复滚动位置
restoreScrollPosition();

// 手动重置滚动位置到顶部
resetScrollPosition();
```

## 功能特性

- ✅ 自动保存和恢复分页状态
- ✅ 自动保存和恢复滚动位置
- ✅ 搜索/筛选时自动重置分页
- ✅ 切换页码时自动重置滚动位置到顶部
- ✅ 支持自定义滚动容器
- ✅ 完善的错误处理
- ✅ TypeScript 类型支持
- ✅ 页面卸载时自动保存状态

## 滚动行为说明

### 默认行为
- **切换页码时**：自动滚动到顶部，方便查看新页面内容
- **搜索/筛选时**：自动滚动到顶部，重置分页到第1页
- **页面刷新/重新进入**：恢复到离开时的滚动位置

### 自定义配置
```typescript
// 禁用页码切换时的滚动重置
const { currentPage, pageSize } = usePagination({
  storageKey: 'my-pagination',
  resetScrollOnPageChange: false, // 切换页码时不重置滚动位置
});
```
