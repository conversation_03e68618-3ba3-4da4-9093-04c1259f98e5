import { ref, watch, nextTick, onMounted, onBeforeUnmount, type Ref } from 'vue';

export interface PaginationState {
  currentPage: number;
  pageSize: number;
  scrollTop?: number;
}

export interface UsePaginationOptions {
  /** 本地存储的键名 */
  storageKey: string;
  /** 默认页码，默认为 1 */
  defaultPage?: number;
  /** 默认每页数量，默认为 20 */
  defaultPageSize?: number;
  /** 是否启用本地存储，默认为 true */
  enableStorage?: boolean;
  /** 重置分页的触发条件（响应式变量数组） */
  resetTriggers?: Ref<any>[];
  /** 是否记住滚动位置，默认为 false */
  rememberScrollPosition?: boolean;
  /** 滚动容器的选择器或 ref，默认为 window */
  scrollContainer?: string | Ref<HTMLElement | null>;
  /** 切换页码时是否重置滚动位置到顶部，默认为 true */
  resetScrollOnPageChange?: boolean;
}

export interface UsePaginationReturn {
  /** 当前页码 */
  currentPage: Ref<number>;
  /** 每页数量 */
  pageSize: Ref<number>;
  /** 手动重置分页到第一页 */
  resetPagination: () => void;
  /** 手动保存分页状态 */
  savePaginationState: () => void;
  /** 手动恢复分页状态 */
  restorePaginationState: () => void;
  /** 手动保存滚动位置 */
  saveScrollPosition: () => void;
  /** 手动恢复滚动位置 */
  restoreScrollPosition: () => void;
  /** 重置滚动位置到顶部 */
  resetScrollPosition: () => void;
}

/**
 * 分页状态管理 Hook
 * 支持本地存储记忆、自动重置等功能
 */
export function usePagination(options: UsePaginationOptions): UsePaginationReturn {
  const {
    storageKey,
    defaultPage = 1,
    defaultPageSize = 20,
    enableStorage = true,
    resetTriggers = [],
    rememberScrollPosition = false,
    scrollContainer,
    resetScrollOnPageChange = true,
  } = options;

  // 获取滚动容器元素
  const getScrollContainer = (): HTMLElement | Window => {
    if (!scrollContainer) return window;
    if (typeof scrollContainer === 'string') {
      const element = document.querySelector(scrollContainer) as HTMLElement;
      return element || window;
    }

    const containerElement = scrollContainer.value;
    if (containerElement) {
      // 尝试找到 Ant Design 表格的滚动容器
      const antTableBody = containerElement.querySelector('.ant-table-body') as HTMLElement;
      if (antTableBody) {
        return antTableBody;
      }
      return containerElement;
    }
    return window;
  };

  // 从本地存储恢复分页状态
  const restorePaginationState = (): PaginationState => {
    if (!enableStorage) {
      return { currentPage: defaultPage, pageSize: defaultPageSize };
    }

    try {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        const parsed = JSON.parse(saved);
        return {
          currentPage: parsed.currentPage || defaultPage,
          pageSize: parsed.pageSize || defaultPageSize,
          scrollTop: parsed.scrollTop || 0,
        };
      }
    } catch (error) {
      console.warn(`恢复分页状态失败 [${storageKey}]:`, error);
    }
    return { currentPage: defaultPage, pageSize: defaultPageSize, scrollTop: 0 };
  };

  // 保存分页状态到本地存储
  const savePaginationState = (page?: number, size?: number, scrollTop?: number) => {
    if (!enableStorage) return;

    try {
      const container = getScrollContainer();
      const currentScrollTop = scrollTop ?? (
        container === window
          ? window.pageYOffset || document.documentElement.scrollTop
          : (container as HTMLElement).scrollTop
      );

      const state = {
        currentPage: page ?? currentPage.value,
        pageSize: size ?? pageSize.value,
        ...(rememberScrollPosition && { scrollTop: currentScrollTop }),
      };
      localStorage.setItem(storageKey, JSON.stringify(state));
    } catch (error) {
      console.warn(`保存分页状态失败 [${storageKey}]:`, error);
    }
  };

  // 初始化分页状态
  const initialState = restorePaginationState();
  const currentPage = ref(initialState.currentPage);
  const pageSize = ref(initialState.pageSize);

  // 保存滚动位置
  const saveScrollPosition = () => {
    if (!rememberScrollPosition || !enableStorage) return;
    savePaginationState();
  };

  // 重置滚动位置到顶部
  const resetScrollPosition = () => {
    nextTick(() => {
      const container = getScrollContainer();
      if (container === window) {
        window.scrollTo(0, 0);
      } else {
        (container as HTMLElement).scrollTop = 0;
      }
    });
  };

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    if (!rememberScrollPosition || !enableStorage) return;

    const state = restorePaginationState();
    if (state.scrollTop !== undefined && state.scrollTop > 0) {
      // 使用多次 nextTick 确保 DOM 完全渲染
      nextTick(() => {
        nextTick(() => {
          setTimeout(() => {
            const container = getScrollContainer();
            if (container === window) {
              window.scrollTo(0, state.scrollTop!);
            } else {
              const element = container as HTMLElement;
              if (element && element.scrollHeight > state.scrollTop!) {
                element.scrollTop = state.scrollTop!;
              }
            }
          }, 100); // 延迟 100ms 确保表格数据渲染完成
        });
      });
    }
  };

  // 重置分页到第一页
  const resetPagination = () => {
    currentPage.value = 1;
  };

  // 监听分页变化，自动保存到本地存储
  if (enableStorage) {
    watch([currentPage, pageSize], ([newPage, newSize], [oldPage]) => {
      savePaginationState(newPage, newSize);

      // 如果页码发生变化且启用了滚动重置，则重置滚动位置
      if (resetScrollOnPageChange && newPage !== oldPage) {
        resetScrollPosition();
      }
    });
  }

  // 监听重置触发条件，自动重置分页
  if (resetTriggers.length > 0) {
    watch(resetTriggers, () => {
      resetPagination();
    });
  }

  // 自动保存滚动位置（在页面卸载前）
  if (rememberScrollPosition && enableStorage) {
    const handleBeforeUnload = () => {
      saveScrollPosition();
    };

    onMounted(() => {
      window.addEventListener('beforeunload', handleBeforeUnload);
      // 页面加载完成后恢复滚动位置
      restoreScrollPosition();
    });

    onBeforeUnmount(() => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      saveScrollPosition();
    });
  }

  return {
    currentPage,
    pageSize,
    resetPagination,
    savePaginationState: () => savePaginationState(),
    restorePaginationState: () => {
      const state = restorePaginationState();
      currentPage.value = state.currentPage;
      pageSize.value = state.pageSize;
    },
    saveScrollPosition,
    restoreScrollPosition,
    resetScrollPosition,
  };
}
