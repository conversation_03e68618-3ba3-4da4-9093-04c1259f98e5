# JSJ-UI 智能财务系统

<div align="center">
  <h2>🤖 AI驱动的企业级财务管理平台</h2>
  <p>基于 Vue 3 + TypeScript + Ant Design Vue 构建的现代化智能财务系统</p>
</div>

## 📖 项目简介

JSJ-UI 是一款现代化的企业级智能财务管理系统，采用 Vue 3 + Vben Admin v5.0 + TypeScript 技术栈构建。系统专注于AI驱动的财务会计业务，提供智能化的记账助手、凭证管理、发票处理等核心功能，帮助企业实现财务数字化转型。

## ✨ 核心功能

### 🤖 AI智能助手
- 实时对话式财务助手
- 支持文件上传和多轮对话
- 语音录入功能
- 智能业务处理建议

### 📊 凭证管理
- 智能凭证生成与编辑
- 批量凭证处理
- 凭证审核工作流
- 支持部分数据生成凭证

### 🧾 发票处理
- OCR智能识别
- 发票智能分类
- 批量导入处理
- 货物维度展示

### 🏪 银行回单
- 自动对账功能
- 智能匹配处理
- OSS文件存储

### 🏢 多公司管理
- 企业级多租户隔离
- 公司数据独立管理
- 第三方平台集成
- 数据源灵活切换

### 🔐 权限管理
- 基于角色的权限控制
- 动态菜单配置
- 操作权限细粒度控制

## 🛠 技术栈

- **前端框架**: Vue 3.5+, TypeScript 5.8+, Vite 6.2+
- **UI组件库**: Ant Design Vue 4.2+
- **状态管理**: Pinia 2.3+
- **样式方案**: TailwindCSS 3.4+ + SCSS
- **构建系统**: Turbo 2.5+ monorepo + pnpm workspaces
- **测试框架**: Vitest (单元测试), Playwright (E2E测试)
- **实时通信**: WebSocket (AI聊天功能)

## 📁 项目结构

```
jsj-ui/
├── apps/
│   ├── web-antd/              # 主应用 (Ant Design Vue)
│   └── web-naive/             # 备用UI实现 (Naive UI)
├── packages/
│   ├── @core/                 # 核心业务逻辑
│   ├── @vben/                 # 框架核心包
│   ├── icons/                 # 图标组件
│   ├── locales/               # 国际化
│   ├── types/                 # 共享类型定义
│   └── utils/                 # 工具函数
├── CHANGELOG.md               # 更新日志
├── CLAUDE.md                  # 项目开发指南
└── README.md                  # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- pnpm 8+

### 安装依赖
```bash
# 克隆项目
git clone [项目地址]

# 进入项目目录
cd jsj-ui

# 启用 corepack
corepack enable

# 安装依赖
pnpm install
```

### 开发调试
```bash
# 启动开发服务器
pnpm dev

# 启动主应用
pnpm --filter=@vben/web-antd dev
```

### 构建部署
```bash
# 构建所有包
pnpm build

# 构建主应用
pnpm --filter=@vben/web-antd build

# 预览构建结果
pnpm preview
```

## 🔧 开发工具

### 代码质量
```bash
# 代码检查
pnpm lint

# 自动修复
pnpm lint:fix

# 类型检查
pnpm typecheck

# 代码格式化
pnpm format
```

### 测试```bash
# 单元测试
pnpm test

# E2E测试
pnpm test:e2e

# 测试覆盖率
pnpm test:coverage
```

### API相关
```bash
# 生成API类型
pnpm gen:api
```

## 📋 开发规范

### Git 提交规范
```
feat: 新增功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
perf: 性能优化
test: 测试相关
chore: 构建/工具链相关
```

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 代码规范
- 组件采用 `<script setup>` 语法
- 优先使用 Composition API
- API调用统一管理在 `/src/api/` 目录

## 📊 更新日志

详细的版本更新记录请查看 [CHANGELOG.md](./CHANGELOG.md)

## 🤝 参与贡献

欢迎参与项目贡献！请遵循以下流程：

1. Fork 本项目
2. 创建功能分支: `git checkout -b feat/新功能`
3. 提交更改: `git commit -am 'feat: 添加新功能'`
4. 推送到分支: `git push origin feat/新功能`
5. 提交 Pull Request

## 🌐 浏览器支持

- 推荐使用 Chrome 80+ 进行开发
- 支持现代浏览器，不支持 IE
- 支持 Edge、Firefox、Safari 最新版本


## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发起 Discussion
- 邮件联系

---

<div align="center">
  <p>💖 感谢使用 JSJ-UI 智能财务系统</p>
</div>
