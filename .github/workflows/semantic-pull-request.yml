name: <PERSON><PERSON><PERSON> Pull Request

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

jobs:
  main:
    name: <PERSON><PERSON><PERSON> Pull Request
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ubuntu-latest
    steps:
      - name: Validate PR title
        uses: amannn/action-semantic-pull-request@v5
        with:
          wip: true
          subjectPattern: ^(?![A-Z]).+$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            doesn't start with an uppercase character.
          requireScope: false
          types: |
            fix
            feat
            docs
            style
            refactor
            perf
            test
            build
            ci
            chore
            revert
            types
            release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
